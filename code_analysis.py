#!/usr/bin/env python3
"""
Static Code Analysis for JWT Secret Disclosure Vectors
Analyze the source code for potential ways to extract the JWT secret
"""

import os
import re
import json

def analyze_error_handling():
    """Analyze error handling for potential secret disclosure"""
    print("[+] 🔍 Analyzing error handling...")
    
    # Read app.js
    try:
        with open('app.js', 'r') as f:
            app_content = f.read()
        
        print("✅ Found app.js")
        
        # Look for error handling patterns
        error_patterns = [
            r'catch\s*\(\s*(\w+)\s*\)\s*{([^}]+)}',
            r'console\.error\([^)]+\)',
            r'res\.status\(500\)',
            r'Internal Server Error',
            r'error\.',
        ]
        
        for pattern in error_patterns:
            matches = re.findall(pattern, app_content, re.DOTALL | re.IGNORECASE)
            if matches:
                print(f"    Found error pattern: {pattern}")
                for match in matches:
                    print(f"        {match}")
        
        # Check if errors might expose environment variables
        if 'console.error' in app_content:
            print("    🎯 POTENTIAL: Server logs errors to console")
            print("    💡 Errors might be visible in server logs or debug mode")
            
    except FileNotFoundError:
        print("❌ app.js not found")

def analyze_environment_usage():
    """Analyze how environment variables are used"""
    print(f"\n[+] 🔍 Analyzing environment variable usage...")
    
    files_to_check = ['app.js', 'utils.js', 'package.json', '.env']
    
    for filename in files_to_check:
        try:
            with open(filename, 'r') as f:
                content = f.read()
            
            print(f"✅ Found {filename}")
            
            # Look for environment variable patterns
            env_patterns = [
                r'process\.env\.(\w+)',
                r'process\.env\[[\'""](\w+)[\'""]\]',
                r'JWT_SECRET',
                r'FLAG',
                r'SECRET',
                r'PASSWORD',
                r'KEY',
                r'TOKEN',
            ]
            
            found_vars = set()
            
            for pattern in env_patterns:
                matches = re.findall(pattern, content, re.IGNORECASE)
                if matches:
                    for match in matches:
                        if isinstance(match, str):
                            found_vars.add(match)
                        else:
                            found_vars.add(pattern)
            
            if found_vars:
                print(f"    Environment variables found: {list(found_vars)}")
                
                # Check for default values
                default_patterns = [
                    r'process\.env\.(\w+)\s*\|\|\s*[\'"""]([^\'"""]+)[\'"""]',
                    r'(\w+)\s*=\s*process\.env\.(\w+)\s*\|\|\s*[\'"""]([^\'"""]+)[\'"""]',
                ]
                
                for pattern in default_patterns:
                    matches = re.findall(pattern, content)
                    if matches:
                        print(f"    🎯 DEFAULT VALUES FOUND:")
                        for match in matches:
                            print(f"        {match}")
            
        except FileNotFoundError:
            print(f"❌ {filename} not found")

def analyze_jwt_implementation():
    """Analyze JWT implementation for weaknesses"""
    print(f"\n[+] 🔍 Analyzing JWT implementation...")
    
    try:
        with open('utils.js', 'r') as f:
            utils_content = f.read()
        
        print("✅ Found utils.js")
        
        # Look for JWT-related code
        jwt_patterns = [
            r'jsonwebtoken\.sign\([^)]+\)',
            r'jsonwebtoken\.verify\([^)]+\)',
            r'JWT_SECRET',
            r'algorithm\s*:\s*[\'"""]([^\'"""]+)[\'"""]',
            r'expiresIn\s*:\s*[\'"""]([^\'"""]+)[\'"""]',
        ]
        
        for pattern in jwt_patterns:
            matches = re.findall(pattern, utils_content, re.DOTALL)
            if matches:
                print(f"    JWT pattern found: {pattern}")
                for match in matches:
                    print(f"        {match}")
        
        # Check for potential vulnerabilities
        if 'catch (err)' in utils_content and 'return regenerate()' in utils_content:
            print("    🎯 VULNERABILITY: JWT verification errors trigger regeneration")
            print("    💡 This means ANY invalid JWT gets a fresh token")
        
        # Check if secret is hardcoded anywhere
        hardcoded_patterns = [
            r'[\'"""]([a-zA-Z0-9+/=]{20,})[\'"""]',  # Base64-like strings
            r'[\'"""]([a-zA-Z0-9]{32,})[\'"""]',     # Long hex-like strings
        ]
        
        for pattern in hardcoded_patterns:
            matches = re.findall(pattern, utils_content)
            if matches:
                print(f"    🔍 Potential hardcoded secrets:")
                for match in matches:
                    if len(match) > 10:  # Only show longer strings
                        print(f"        {match}")
        
    except FileNotFoundError:
        print("❌ utils.js not found")

def analyze_static_files():
    """Analyze static file serving for potential disclosure"""
    print(f"\n[+] 🔍 Analyzing static file serving...")
    
    try:
        with open('app.js', 'r') as f:
            app_content = f.read()
        
        # Look for static file serving
        static_patterns = [
            r'express\.static\([\'"""]([^\'"""]+)[\'"""]',
            r'app\.use\([^)]*static[^)]*\)',
        ]
        
        for pattern in static_patterns:
            matches = re.findall(pattern, app_content)
            if matches:
                print(f"    Static directory: {matches}")
                
                # Check if static directory exists and what's in it
                for static_dir in matches:
                    if os.path.exists(static_dir):
                        print(f"    ✅ Static directory exists: {static_dir}")
                        
                        # List files in static directory
                        try:
                            files = os.listdir(static_dir)
                            print(f"        Files: {files}")
                            
                            # Check for sensitive files
                            sensitive_files = ['.env', 'config.js', 'config.json', '.git', 'backup']
                            for file in files:
                                if any(sensitive in file.lower() for sensitive in sensitive_files):
                                    print(f"        🎯 SENSITIVE FILE: {file}")
                                    
                        except PermissionError:
                            print(f"        ❌ Permission denied to list {static_dir}")
                    else:
                        print(f"    ❌ Static directory not found: {static_dir}")
        
    except FileNotFoundError:
        print("❌ app.js not found")

def analyze_package_json():
    """Analyze package.json for potential information disclosure"""
    print(f"\n[+] 🔍 Analyzing package.json...")
    
    try:
        with open('package.json', 'r') as f:
            package_data = json.load(f)
        
        print("✅ Found package.json")
        
        # Look for scripts that might expose secrets
        if 'scripts' in package_data:
            print("    Scripts found:")
            for script_name, script_cmd in package_data['scripts'].items():
                print(f"        {script_name}: {script_cmd}")
                
                # Check for environment variable usage in scripts
                if 'JWT_SECRET' in script_cmd or 'SECRET' in script_cmd:
                    print(f"        🎯 SECRET IN SCRIPT: {script_name}")
        
        # Look for dependencies that might have vulnerabilities
        if 'dependencies' in package_data:
            print("    Dependencies:")
            for dep, version in package_data['dependencies'].items():
                print(f"        {dep}: {version}")
                
                # Check for known vulnerable packages
                vulnerable_packages = ['jsonwebtoken']
                if dep in vulnerable_packages:
                    print(f"        🔍 Check {dep} version {version} for vulnerabilities")
        
        # Check for any hardcoded secrets in package.json
        package_str = json.dumps(package_data, indent=2)
        secret_patterns = [
            r'[\'"""]([a-zA-Z0-9+/=]{20,})[\'"""]',
            r'secret',
            r'password',
            r'key',
            r'token',
        ]
        
        for pattern in secret_patterns:
            matches = re.findall(pattern, package_str, re.IGNORECASE)
            if matches and pattern not in ['secret', 'password', 'key', 'token']:
                print(f"    🔍 Potential secrets: {matches}")
        
    except FileNotFoundError:
        print("❌ package.json not found")
    except json.JSONDecodeError:
        print("❌ Invalid JSON in package.json")

def check_file_permissions():
    """Check file permissions for potential security issues"""
    print(f"\n[+] 🔍 Checking file permissions...")
    
    files_to_check = ['.env', 'app.js', 'utils.js', 'package.json']
    
    for filename in files_to_check:
        if os.path.exists(filename):
            stat_info = os.stat(filename)
            permissions = oct(stat_info.st_mode)[-3:]
            
            print(f"    {filename}: {permissions}")
            
            # Check for overly permissive permissions
            if filename == '.env' and permissions != '600':
                print(f"        🎯 INSECURE: .env should have 600 permissions, has {permissions}")
            
            if int(permissions[2]) > 4:  # World readable
                print(f"        🔍 World readable: {filename}")

def main():
    print("🔍 Static Code Analysis for JWT Secret Disclosure")
    print("=" * 60)
    
    # Change to the directory containing the source files
    if os.path.exists('/Users/<USER>/Downloads/dist'):
        os.chdir('/Users/<USER>/Downloads/dist')
        print(f"✅ Changed to source directory")
    else:
        print(f"❌ Source directory not found")
        return
    
    # Run all analysis functions
    analyze_error_handling()
    analyze_environment_usage()
    analyze_jwt_implementation()
    analyze_static_files()
    analyze_package_json()
    check_file_permissions()
    
    print(f"\n📊 Static Code Analysis Complete!")
    print(f"\n💡 Potential attack vectors:")
    print(f"   1. Server error messages might expose secrets")
    print(f"   2. Static file serving might expose .env")
    print(f"   3. JWT implementation has regeneration on any error")
    print(f"   4. Check if .env file is accessible via web")
    print(f"   5. Look for backup files or git repositories")

if __name__ == "__main__":
    main()
