<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <link href="/css/output.css" rel="stylesheet" />
    <title>Not Kahoot</title>
</head>
<body class = "overflow-hidden w-screen h-screen relative">
    <div class = "flex flex-col gap-4 items-center justify-center h-screen w-screen bg-gradient-to-r from-blue-500 to-purple-500 text-white">
        <h1 class = "text-7xl font-bold z-10 relative">Welcome to Not Kahoot</h1>
        <p class = "text-2xl z-10 relative">Press Start to begin the not quiz</p>
        <a class = "contents" href = "/quiz">
            <button id="start-button" class = "text-xl z-10 relative px-20 bg-red-300 font-bold cursor-pointer hover:brightness-90 transition py-2 rounded-xl shadow-xl">Start</button>
        </a> 
        <div class = "w-96  aspect-square bg-blue-800 opacity-50 absolute top-24 left-12 rounded-full shadow-lg"></div>
        <div class = "w-72  aspect-square bg-blue-800 opacity-50 absolute bottom-36 rotate-12 right-24 shadow-lg"></div>

        </div>
    </div>
    <!-- Dont judge the design :c I'm on 3hrs of sleep here -->
</body>
</html>