<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <link href="/css/output.css" rel="stylesheet" />
    <title>Not Kahoot</title>
</head>
<body class = "overflow-x-hidden overflow-y-auto relative bg-gradient-to-r from-blue-500 to-purple-500">
    <div class = "text-white text-4xl font-bold fixed top-0 left-0 w-screen py-4 bg-gradient-to-r from-blue-600 to-purple-600 shadow-xl">
        <p class = "absolute left-0 px-4 text-xl top-1/2 -translate-y-1/2 text-blue-100 question-part">
            Question <%= question.questionNo + 1%> Part <%= question.questionPart + 1%> 
        </p>
        <p class="text-center question">
            
            <%= question.question.question %>
        </p>
        <div class="absolute top-full my-4 left-12 text-2xl font-bold text-white">
            Score: <span class = "score ">0</span>
        </div>
    </div>
    
    <div class="timer 
    mt-32 mb-12 mx-auto rounded-full bg-blue-700 text-white flex items-center justify-center font-bold w-32 text-5xl  aspect-square p-4">
        30
    </div>
    <div class = "grid grid-cols-3 grid-rows-3 gap-4 px-12 h-72">
        <% let colors = ['bg-red-400', 'bg-green-400', 'bg-yellow-400', 'bg-blue-400', 'bg-purple-400', 'bg-pink-400', 'bg-orange-400', 'bg-teal-400', 'bg-indigo-400']; %>
        <% let defaultClass = 'w-full h-full flex items-center justify-center font-bold text-2xl text-white rounded-lg shadow-lg hover:scale-105 transition-transform duration-300 cursor-pointer disabled:brightness-90 disabled:scale-100' %>
        <% for (let i = 0; i < question.question.options.length; i++) { %>
            <button class="<%= defaultClass %> <%= colors[i] %> option" value="<%= question.question.options[i] %>">
                <%= question.question.options[i] %>
            </button>
        <% } %>

    </div>
    <a class = "contents" href = "/restart">
        <button class = "text-xl z-10 relative px-20 bg-red-600 text-blue-100 font-bold cursor-pointer hover:brightness-90 transition py-2 rounded-xl shadow-xl my-8 mx-auto block">Restart</button>
    </a> 

    <!-- Dont judge the design :c I'm on 3hrs of sleep here -->
     <script>
        const options = document.querySelectorAll('.option');
        const timerDisplay = document.querySelector('.timer');
        const questionDisplay = document.querySelector('.question');
        const questionPartDisplay = document.querySelector('.question-part');
        const scoreDisplay = document.querySelector('.score');
        let questionNo = <%= question.questionNo %>;
        let questionPart = <%= question.questionPart %>;
        let timerInterval
        let timeLeft = 30; // 30 seconds timer
        function stopTimer() {
            clearInterval(timerInterval); // Clear the timer interval
        }
        function startTimer() {
            timeLeft = 30; // Reset the timer to 30 seconds
            timerDisplay.textContent = timeLeft; // Display the initial time
            timerInterval = setInterval(() => {
                timeLeft--;
                timerDisplay.textContent = timeLeft;
                if (timeLeft <= 0) {
                    // Handle timeout logic here
                    submitAnswer("");
                }
            }, 1000);
        }
        function submitAnswer(selectedOption){
            stopTimer(); // Stop the timer when it reaches 0
            options.forEach(option => option.disabled = true); // Disable all options after selection
            fetch('/submit',
            {
                method: 'POST',
                headers: {
                    'Content-Type': 'application/json'
                },
                body: JSON.stringify({ 
                    answer: selectedOption, 
                    questionNo, 
                    questionPart 
                })
            })
            .then(response => response.json())
            .then(data => {
                scoreDisplay.textContent = data.score; // Update the score display
                if (data.correct) {
                    questionDisplay.textContent = "Correct!";
                    questionDisplay.classList.add('text-green-500');
                    if (data.message) questionDisplay.textContent = data.message; // Display the message if provided
                } else {
                    questionDisplay.textContent = "Incorrect!";
                    questionDisplay.classList.add('text-red-500');
                }
                setTimeout(() => {
                    questionDisplay.textContent = ""; // Clear the alert message
                    questionDisplay.classList.remove('text-green-500', 'text-red-500');
                    // Reset the timer and options for the next question
                    if (data.nextQuestion) {
                        questionNo = data.nextQuestion.questionNo; // Update question number
                        questionPart = data.nextQuestion.questionPart; // Update question part
                        configureQuesiton(data.nextQuestion); // Configure the next question
                        timerDisplay.textContent = timeLeft;
                        options.forEach(option => option.disabled = false); // Enable all options again
                        startTimer(); // Restart the timer for the next question
                    }
                    else{
                        questionDisplay.textContent = "Quiz Over!"; // Display quiz over message
                        timerDisplay.textContent = ""; // Clear the timer display
                        options.forEach(option => option.disabled = true); // Disable all options
                    }
                    
                }, 2000);
            })
        }

        function configureQuesiton(question){
            questionDisplay.textContent = question.question.question; // Set the question text
            questionPartDisplay.textContent = `Question ${question.questionNo + 1} Part ${question.questionPart + 1}`; // Set the question part text
            options.forEach((option, index) => {
                option.textContent = question.question.options[index]; // Set the option text
                option.value = question.question.options[index]; // Set the value of the button to the option text
                option.addEventListener('click', handleOptionClick); // Add click event listener to each option
            });
        }

        function handleOptionClick(event){
            const selectedOption = this.value;
            console.log(selectedOption);
            submitAnswer(selectedOption);
        }

        function initialise(){
            options.forEach(option => {
                option.addEventListener('click', handleOptionClick); // Add click event listener to each option
            });
            startTimer(); 
        }
        initialise(); 
     </script>
</body>
</html>