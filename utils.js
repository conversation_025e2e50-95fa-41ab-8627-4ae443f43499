const jsonwebtoken = require('jsonwebtoken');
const uuid = require('uuid');
// Generate a new JWT and initialize user data if needed
function generateJWT(userUuid, score = 0, answered = []) {
    userUuid = userUuid ?? uuid.v4();
    const data = { userUuid, answered, score };
    const jwt = jsonwebtoken.sign(data, process.env.JWT_SECRET, { expiresIn: '1h',algorithm: 'HS256' });
    return { userUuid, jwt, score, answered };
}

// shuffle array
function shuffle(array) {
    let currentIndex = array.length;

    while (currentIndex !== 0) {
        const randomIndex = Math.floor(Math.random() * currentIndex);
        currentIndex--;
        [array[currentIndex], array[randomIndex]] = [array[randomIndex], array[currentIndex]];
    }

    return array;
}

// Get sample questions and answers
function getQuestionsAndAnswers() {
    // Each question has 9 options
    const questions = [
        [
            {
                question: "What is 1+1?",
                options: ["1", "2", "3", "4", "5", "6", "7", "8", "9"]
            },
            {
                question: "What is 2+2?",
                options: ["1", "2", "3", "4", "5", "6", "7", "8", "9"]
            }
        ],
        [
            {
                question: "What is the capital of France?",
                options: ["Berlin", "Madrid", "Paris", "Rome", "London", "Lisbon", "Amsterdam", "Brussels", "Vienna"]
            },
            {
                question: "What is the capital of Germany?",
                options: ["Berlin", "Madrid", "Paris", "Rome", "London", "Lisbon", "Amsterdam", "Brussels", "Vienna"]
            }
        ],
        [
            {
                question: "What is Newton's third law of motion?",
                options: ["For every action, there is an equal and opposite reaction", 
                    "An object in motion stays in motion", 
                    "Force equals mass times acceleration",
                    "Energy cannot be created or destroyed",
                    "The total energy of an isolated system remains constant",
                    "The entropy of an isolated system never decreases",
                    "The laws of thermodynamics govern the behavior of energy",
                    "The speed of light is constant in a vacuum",
                    "Gravity is a force of attraction between two masses"]
            },
            {
                question: "What is the theory of relativity?",
                options: ["The laws of physics are the same for all observers", 
                    "Time and space are relative concepts",
                    "Mass and energy are interchangeable",
                    "The speed of light is constant in a vacuum",
                    "Gravity is a curvature of spacetime",
                    "The universe is expanding",
                    "Black holes are regions of spacetime with extreme gravitational pull",
                    "Time dilation occurs near massive objects",
                    "The twin paradox illustrates time dilation"]
            }
        ]
        // Baba got lazy to finish the rest of the questions. Oh well.
    ]

    const answers = [
        ["2","4"],
        ["Paris", "Berlin"],
        ["For every action, there is an equal and opposite reaction","Time and space are relative concepts"]
    ];

    return { questions, answers };
}

// Check JWT in cookies, verify or generate new if invalid
function checkAndGenerateJWT(req, res) {
    // Checks if the JWT is present in cookies
    // If not, generate a new one and set it in cookies
    // If present, verifies it and return the decoded data
    let token = req.cookies.jwt;
    let decodedData = null;

    const regenerate = () => {
        const { userUuid, jwt, score, answered } = generateJWT();
        res.cookie('jwt', jwt, { httpOnly: true, secure: true });
        return { jwt, decodedRes: { userUuid, score, answered } };
    };

    if (!token) return regenerate();

    try {
        decodedData = jsonwebtoken.verify(token, process.env.JWT_SECRET);
    } catch (err) {
        return regenerate();
    }

    return { jwt: token, decodedRes: decodedData };
}

function getNextQuestion(questions, answered) {
    // Find the next unanswered question
    for (let i = 0; i < questions.length; i++) {
        for (let j = 0; j < questions[i].length; j++) {
            const questionKey = [i, j].join(',');
            if (!answered.includes(questionKey)) {
                return { question: questions[i][j], questionNo: i, questionPart: j };
            }
        }
    }
    return null;
}

module.exports = {
    generateJWT,
    shuffle,
    getQuestionsAndAnswers,
    checkAndGenerateJWT,
    getNextQuestion
  };