const express = require('express');
const jsonwebtoken = require('jsonwebtoken')
const fs = require('fs');
const { type } = require('os');
const cookieParser = require('cookie-parser');
const { getQuestionsAndAnswers, generateJWT, checkAndGenerateJWT, getNextQuestion } = require('./utils.js')
require('dotenv').config();

const FLAG = process.env.FLAG || 'SSMCTF{test}'
const app = express()
const port = 3000

app.use(express.json())
app.use(express.static('public'));
app.use(cookieParser())
app.set('views', './views')



app.get('/', (req, res) => {
    res.render('index.ejs')
})

app.get('/quiz', (req, res) => {
    const { questions } = getQuestionsAndAnswers()
    const { decodedRes: { answered } } = checkAndGenerateJWT(req, res)
    const nextQuestion = getNextQuestion(questions, answered);
    if (!nextQuestion) {
        // Restart the quiz if all questions are answered
        res.redirect('/restart');
        return;
    }
    res.render('quiz.ejs', {
        question: nextQuestion,
    })
}
)
app.get('/restart', (req, res) => {
    res.clearCookie('jwt');
    res.redirect('/quiz');
})
app.post('/submit', (req, res) => {
    try {
        const questionNo = req.body.questionNo
        const questionPart = req.body.questionPart
        const questionKey = [questionNo, questionPart].join(',')
        let { decodedRes: { userUuid, score, answered } } = checkAndGenerateJWT(req, res)
        if (answered.includes(questionKey)) {
            // If the question has already been answered, return an error response
            res.status(400).send('Question already answered');
            return;
        }
        const {questions, answers} = getQuestionsAndAnswers(userUuid)
        const userAnswer = req.body.answer
        const correctAnswer = answers[questionNo][questionPart]

        if (typeof userAnswer !== 'string') {
            res.status(400).send('Invalid answer format');
            return;
        }
        if (correctAnswer === undefined) {
            res.status(400).send('Question not found');
            return;
        }
        if (userAnswer == correctAnswer) {
            // If correct answer, increment score and add current question to answered list.
            // Regenerates JWT with new score and answered list.
            score += 1;
            answered.push(questionKey);
            const { jwt } = generateJWT(userUuid, score, answered);
            res.cookie('jwt', jwt, { httpOnly: true, secure: true });
            const nextQuestion = getNextQuestion(questions, answered);
            if (score >= 20){
                // If score is 20 or more, return the flag as a reward. I made 20 questions right?
                res.status(200).json({ correct: true, score, nextQuestion, message: `Congratulations! You've completed the quiz! Heres the Flag: ${FLAG}` });
                return;
            }
            res.status(200).json({ correct: true, score, nextQuestion });
        } else {
            // If wrong answer, do not increment score, but add current question to answered list, so it cant be answered again
            answered.push(questionKey);
            const { jwt } = generateJWT(userUuid, score, answered);
            const nextQuestion = getNextQuestion(questions, answered);
            res.status(200).json({ correct: false, score, nextQuestion });
        }
    }
    catch (error) {
        console.error('Error processing request:', error);
        res.status(500).send('Internal Server Error');
        return;
    }
})

try {
    app.listen(port, () => {
        console.log(`Example app listening on port ${port}`)
    })
}
catch (error) {
    console.error('Error starting the server:', error);
    process.exit(1); // Exit the process with a failure code
}