{"name": "very_easy_test", "version": "1.0.0", "main": "index.js", "scripts": {"build:css": "tailwindcss -i ./public/css/input.css -o ./public/css/output.css --watch", "dev:server": "nodemon app.js", "dev": "concurrently \"tailwindcss -i ./public/css/input.css -o ./public/css/output.css --watch\" \"nodemon app.js\""}, "author": "", "license": "ISC", "description": "", "dependencies": {"@tailwindcss/postcss": "^4.1.5", "cookie-parser": "^1.4.7", "dotenv": "^16.5.0", "ejs": "^3.1.10", "express": "^5.1.0", "jsonwebtoken": "^9.0.2", "uuid": "^11.1.0"}, "devDependencies": {"@tailwindcss/cli": "^4.1.5", "autoprefixer": "^10.4.21", "concurrently": "^9.1.2", "nodemon": "^3.1.10", "postcss": "^8.5.3", "tailwindcss": "^4.1.5"}}